import { SkillCalculator } from "../../types/skill";
import { CharacterData, CharacterSkill } from "../../types/character";
import { SkillOptions, CustomBuffs } from "../../types/skill";

export class DefaultSkillCalculator extends SkillCalculator {
  constructor(
    character: CharacterData,
    skill: CharacterSkill,
    skillLevel: number,
    options: SkillOptions
  ) {
    super(character, skill, skillLevel, options);
  }
  
  // 获取自定义增益
  protected getCustomBuffs(): CustomBuffs {
    return this.options.customBuffs || {};
  }
  
  // 应用自定义增益到攻击力
  protected applyCustomBuffsToAttack(baseAttack: number): number {
    const buffs = this.getCustomBuffs();
    let result = baseAttack;
    
    // 攻击力直接加算
    if (buffs.attackFirstValue) {
      result += buffs.attackFirstValue;
    }
    
    // 攻击力直接乘算 (百分比)
    if (buffs.attackFirstRatio) {
      result *= (1 + buffs.attackFirstRatio / 100);
    }
    
    // 攻击力最终加算
    if (buffs.attackFinalValue) {
      result += buffs.attackFinalValue;
    }
    
    return result;
  }
  
  // 应用自定义增益到敌人防御
  protected applyCustomBuffsToEnemyDef(enemyDef: number): number {
    const buffs = this.getCustomBuffs();
    let result = enemyDef;
    
    // 敌人防御力减少
    if (buffs.enemyDefLoss) {
      result = Math.max(0, result - buffs.enemyDefLoss);
    }
    
    return result;
  }
  
  // 应用自定义增益到敌人法抗
  protected applyCustomBuffsToEnemyRes(enemyRes: number): number {
    const buffs = this.getCustomBuffs();
    let result = enemyRes;
    
    // 敌人法抗减少
    if (buffs.enemyResLoss) {
      result = Math.max(0, result - buffs.enemyResLoss);
    }
    
    return result;
  }
  
  // 应用自定义增益到伤害
  protected applyCustomBuffsToDamage(damage: number, damageType: string = 'normal'): number {
    const buffs = this.getCustomBuffs();
    let result = damage;
    
    // 通用伤害增幅
    if (buffs.damageAmplifyRatio) {
      result *= (1 + buffs.damageAmplifyRatio / 100);
    }
    
    // 根据伤害类型应用特定增幅
    if (damageType === 'physical' && buffs.physicalDamageAmplifyRatio) {
      result *= (1 + buffs.physicalDamageAmplifyRatio / 100);
    } else if (damageType === 'magical' && buffs.magicalDamageAmplifyRatio) {
      result *= (1 + buffs.magicalDamageAmplifyRatio / 100);
    } else if (damageType === 'elemental' && buffs.elementalDamageAmplifyRatio) {
      result *= (1 + buffs.elementalDamageAmplifyRatio / 100);
    }
    
    // 敌人脆弱状态
    if (buffs.enemyVulnerableRatio) {
      result *= (1 + buffs.enemyVulnerableRatio / 100);
    }
    
    // 根据伤害类型应用特定脆弱
    if (damageType === 'physical' && buffs.enemyVulnerablePhysicalRatio) {
      result *= (1 + buffs.enemyVulnerablePhysicalRatio / 100);
    } else if (damageType === 'magical' && buffs.enemyVulnerableMagicalRatio) {
      result *= (1 + buffs.enemyVulnerableMagicalRatio / 100);
    } else if (damageType === 'elemental' && buffs.enemyVulnerableElementalRatio) {
      result *= (1 + buffs.enemyVulnerableElementalRatio / 100);
    }
    
    return result;
  }
  
  // 应用自定义增益到攻击速度
  protected applyCustomBuffsToAttackSpeed(attackSpeed: number): number {
    const buffs = this.getCustomBuffs();
    let result = attackSpeed;
    
    // 攻击速度提升
    if (buffs.attackSpeedValue) {
      result += buffs.attackSpeedValue;
    }
    
    return result;
  }
  
  // 应用自定义增益到技力回复
  protected applyCustomBuffsToSkillRecovery(recoveryTime: number): number {
    const buffs = this.getCustomBuffs();
    let result = recoveryTime;
    
    // 技力回复速度提升
    if (buffs.skillPointRecoveryRatio) {
      result /= (1 + buffs.skillPointRecoveryRatio / 100);
    }
    
    return result;
  }
  
  // 基础DPS计算
  getDps(): string {
    return "暂未适配";
  }
  
  // 获取平均DPS
  getAverageDps(): string {
    return "暂未适配";
  }
  
  // 获取总伤害
  getTotalDamage(): string {
    return "暂未适配";
  }
  
  // 获取技能可用指标
  getAvailableMetrics(): string[] {
    return ["dps", "averageDps", "totalDamage"];
  }
} 