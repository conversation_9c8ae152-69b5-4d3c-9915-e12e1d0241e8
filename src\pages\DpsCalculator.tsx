import React, { useEffect, useState } from 'react';
import { Card, Select, Spin, Typography, Descriptions, Alert, InputNumber, Row, Col, Button } from 'antd';
import type { SelectProps } from 'antd';
import { DownOutlined, UpOutlined, ClearOutlined } from '@ant-design/icons';
import DataLoader from '../utils/dataLoader';
import { CharacterData } from '../types/character';
import { EquipmentData } from '../types/equipment';
import { CharacterSkill } from '../types/character';
import { SkillOptions } from '../types/skill';
import SkillResultPanel from '../components/SkillResultPanel';
import { formatPotentialEffect, applyPotentialBonus } from '../utils/potentialHelper';
import { getSubProfessionName } from '../utils/subProfessionHelper';
import { getProfessionName } from '../utils/professionHelper';
import { formatRarity } from '../utils/rarityHelper';

const { Title } = Typography;
const { Option } = Select;

// SP回复类型映射
const SP_TYPE_MAP: Record<string, string> = {
  "INCREASE_WITH_TIME": "自动回复",
  "INCREASE_BY_ATTACK": "攻击回复",
  "INCREASE_WHEN_ATTACK": "攻击回复",
  "INCREASE_WHEN_TAKEN_DAMAGE": "受击回复",
  "SPECIAL_RECOVERY": "特殊回复"
};

// 技能释放类型映射
const SKILL_TYPE_MAP: Record<string, string> = {
  "MANUAL": "手动触发",
  "AUTO": "自动触发",
  "PASSIVE": "被动技能"
};

// 持续类型映射
const DURATION_TYPE_MAP: Record<string, string> = {
  "NONE": "瞬时",
  "FOREVER": "持续",
  "INSTANT": "瞬时",
  "AMMO": "弹药"
};

interface CharacterLevelData {
  maxHp: number;
  atk: number;
  def: number;
  magicResistance: number;
  cost: number;
  blockCnt: number;
  moveSpeed: number;
  attackSpeed: number;
  baseAttackTime: number;
  respawnTime: number;
  hpRecoveryPerSec: number;
  spRecoveryPerSec: number;
  maxDeployCount: number;
  maxDeckStackCnt: number;
  tauntLevel: number;
  massLevel: number;
  baseForceLevel: number;
  stunImmune: boolean;
  silenceImmune: boolean;
  sleepImmune: boolean;
  originalValues?: {
    [key: string]: number;
  };
  potentialBonuses?: {
    [key: string]: number;
  };
}

// 定义干员卡片组件的Props接口
interface OperatorCardProps {
  index: number;
  onRemove: () => void;
  characters: CharacterData[];
  sharedOptions: SkillOptions;
  onOptionsChange: (newOptions: SkillOptions) => void;
}

// 干员卡片组件
const OperatorCard: React.FC<OperatorCardProps> = ({
  index,
  onRemove,
  characters,
  sharedOptions,
  onOptionsChange
}) => {
  const [selectedCharacter, setSelectedCharacter] = useState<CharacterData | null>(null);
  const [equipment, setEquipment] = useState<EquipmentData[]>([]);
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentData | null>(null);
  const [selectedEquipmentLevel, setSelectedEquipmentLevel] = useState<number>(1);
  const [selectedPhase, setSelectedPhase] = useState<number>(0);
  const [selectedLevel, setSelectedLevel] = useState<number>(1);
  const [selectedPotential, setSelectedPotential] = useState<number>(0);
  const [currentAttributes, setCurrentAttributes] = useState<CharacterLevelData | null>(null);
  const [unlockedEquipment, setUnlockedEquipment] = useState<boolean>(false);
  const [selectedSkill, setSelectedSkill] = useState<CharacterSkill | null>(null);
  const [selectedSkillLevel, setSelectedSkillLevel] = useState<number>(1);
  const [skillInfo, setSkillInfo] = useState<any>(null);
  const [skillOptions, setSkillOptions] = useState<SkillOptions>(sharedOptions);
  const [operatorInfoExpanded, setOperatorInfoExpanded] = useState<boolean>(true);
  const [equipmentInfoExpanded, setEquipmentInfoExpanded] = useState<boolean>(true);
  const [skillInfoExpanded, setSkillInfoExpanded] = useState<boolean>(true);

  // 当共享选项改变时更新本地状态
  useEffect(() => {
    setSkillOptions(sharedOptions);
  }, [sharedOptions]);

  // 当本地选项改变时通知父组件
  const handleLocalOptionsChange = (newOptions: SkillOptions) => {
    setSkillOptions(newOptions);
    onOptionsChange(newOptions);
  };

  // 复制所有原有的处理函数，但添加index前缀以避免冲突
  const handleCharacterChange = (charId: string) => {
    const dataLoader = DataLoader.getInstance();
    const character = dataLoader.getCharacter(charId);
    const characterEquipment = dataLoader.getEquipment(charId);
    setSelectedCharacter(character || null);
    setEquipment(characterEquipment);
    setSelectedEquipment(null);
    setSelectedEquipmentLevel(1);
    setSelectedPotential(0);

    if (character && character.phases) {
      const maxPhase = character.phases.length - 1;
      if (maxPhase >= 0) {
        setSelectedPhase(maxPhase);
        const maxLevel = getCharacterMaxLevel(character, maxPhase);
        setSelectedLevel(maxLevel);
        const attributes = dataLoader.calculateAttributes(character.charId, maxPhase, maxLevel);
        if (attributes) {
          setCurrentAttributes(attributes);
        }
        const hasUnlocked = characterEquipment.some(equip =>
          dataLoader.checkEquipmentUnlocked(character.charId, maxPhase, maxLevel, equip.equipId)
        );
        setUnlockedEquipment(hasUnlocked);
      }
    }
  };

  const handlePhaseChange = (phase: number) => {
    if (!selectedCharacter) return;

    console.log(`Changing phase to: ${phase} for character ${selectedCharacter.name}`);
    setSelectedPhase(phase);

    // 使用辅助函数获取该阶段的最高等级
    const maxLevel = getCharacterMaxLevel(selectedCharacter, phase);
    console.log(`Setting max level for phase ${phase} to: ${maxLevel}`);
    setSelectedLevel(maxLevel);

    // 更新属性
    updateCurrentAttributes(phase, maxLevel);

    // 更新装备解锁状态
    checkEquipmentUnlocked(phase, maxLevel);
  };

  const handleLevelChange = (value: number | null) => {
    if (value === null) return;
    setSelectedLevel(value);
    updateCurrentAttributes(selectedPhase, value);

    // 更新装备解锁状态
    checkEquipmentUnlocked(selectedPhase, value);
  };

  const handleEquipmentChange = (equipId: string) => {
    console.log(`Selected equipment: ${equipId}`);
    const equip = equipment.find(e => e.equipId === equipId);

    if (equip) {
      console.log('Equipment details:', {
        id: equip.equipId,
        name: equip.name,
        typeIcon: equip.typeIcon,
        description: equip.description,
        phases: equip.phases.map(phase => ({
          level: phase.equipLevel,
          attributes: phase.attributeBlackboard
        }))
      });

      // 默认选择第一个可用等级
      const defaultLevel = equip.phases[0]?.equipLevel || 1;
      setSelectedEquipmentLevel(defaultLevel);
      setSelectedEquipment(equip);

      // 立即计算新装备的属性加成，并保留潜能加成
      if (selectedCharacter) {
        updateAttributesWithEquipmentAndPotential(
          selectedCharacter.charId,
          selectedPhase,
          selectedLevel,
          equipId,
          defaultLevel,
          selectedPotential
        );
      }
    } else {
      console.warn(`Equipment with id ${equipId} not found in available equipment list`);
      setSelectedEquipment(null);
      setSelectedEquipmentLevel(1);

      // 移除装备时恢复到基础属性，但保留潜能加成
      if (selectedCharacter) {
        updateAttributesWithEquipmentAndPotential(
          selectedCharacter.charId,
          selectedPhase,
          selectedLevel,
          null,
          null,
          selectedPotential
        );
      }
    }
  };

  const handleEquipmentLevelChange = (level: number) => {
    console.log(`Changing equipment level to: ${level}`);
    setSelectedEquipmentLevel(level);

    // 立即使用新的等级值更新属性，并保留潜能加成
    if (selectedCharacter) {
      updateAttributesWithEquipmentAndPotential(
        selectedCharacter.charId,
        selectedPhase,
        selectedLevel,
        selectedEquipment?.equipId || null,
        level,
        selectedPotential
      );
    }
  };

  // 辅助函数：直接从角色数据中获取指定精英阶段的最高等级
  const getCharacterMaxLevel = (character: CharacterData | null, phase: number): number => {
    if (!character || !character.phases || !character.phases[phase]) {
      return phase === 0 ? 50 : phase === 1 ? 80 : 90; // 默认最高等级
    }

    // 从角色数据中获取该阶段的最高等级
    if (character.phases[phase].attributesKeyFrames &&
        character.phases[phase].attributesKeyFrames.length > 0) {
      const keyFrames = character.phases[phase].attributesKeyFrames;
      return keyFrames[keyFrames.length - 1].level || (phase === 0 ? 50 : phase === 1 ? 80 : 90);
    }

    // 回退到默认最高等级
    return phase === 0 ? 50 : phase === 1 ? 80 : 90;
  };

  // 新的辅助函数，用于一次性计算装备和潜能加成
  const updateAttributesWithEquipmentAndPotential = (
    charId: string,
    phase: number,
    level: number,
    equipId: string | null,
    equipLevel: number | null,
    potentialLevel: number
  ) => {
    const dataLoader = DataLoader.getInstance();

    console.log('Updating attributes with equipment and potential:', {
      character: charId,
      phase,
      level,
      equipment: equipId,
      equipmentLevel: equipLevel,
      potential: potentialLevel
    });

    // 计算基础属性（带装备，如果有的话）
    const baseAttributes = equipId && equipLevel
      ? dataLoader.calculateAttributesWithEquipment(charId, phase, level, equipId, equipLevel)
      : dataLoader.calculateAttributes(charId, phase, level);

    if (!baseAttributes) return;

    // 添加潜能加成
    const attributesWithPotential = applyPotentialBonus(selectedCharacter, baseAttributes, potentialLevel);

    console.log('Updated attributes with equipment and potential:', attributesWithPotential);

    setCurrentAttributes(attributesWithPotential);

    // 同时更新技能选项中的潜能值
    setSkillOptions(prev => ({ ...prev, potentialRank: potentialLevel }));
  };

  const checkEquipmentUnlocked = (phase: number, level: number) => {
    if (!selectedCharacter || equipment.length === 0) {
      setUnlockedEquipment(false);
      return;
    }

    // 检查是否有任何装备已解锁
    const dataLoader = DataLoader.getInstance();
    const hasUnlocked = equipment.some(equip =>
      dataLoader.checkEquipmentUnlocked(selectedCharacter.charId, phase, level, equip.equipId)
    );

    setUnlockedEquipment(hasUnlocked);

    // 如果当前选中的装备未解锁，则清除选择
    if (selectedEquipment && !dataLoader.checkEquipmentUnlocked(
      selectedCharacter.charId, phase, level, selectedEquipment.equipId
    )) {
      setSelectedEquipment(null);
      setSelectedEquipmentLevel(1);
    }
  };

  const updateCurrentAttributes = (phase: number, level: number, potential: number = selectedPotential) => {
    if (!selectedCharacter) return;

    // 使用统一的辅助函数处理属性更新
    updateAttributesWithEquipmentAndPotential(
      selectedCharacter.charId,
      phase,
      level,
      selectedEquipment?.equipId || null,
      selectedEquipmentLevel,
      potential
    );
  };

  // 获取可用的精英化阶段选项
  const getAvailablePhases = () => {
    if (!selectedCharacter || !selectedCharacter.phases) {
      return [0];
    }

    // 根据phases数组长度确定可用精英化阶段
    const phaseCount = selectedCharacter.phases.length;
    return Array.from({ length: phaseCount }, (_, i) => i);
  };

  // 获取该精英化阶段的最小等级
  const getMinLevel = (phase: number) => {
    if (!selectedCharacter || !selectedCharacter.phases || !selectedCharacter.phases[phase] ||
        !selectedCharacter.phases[phase].attributesKeyFrames ||
        selectedCharacter.phases[phase].attributesKeyFrames.length === 0) {
      return 1; // 默认最小等级
    }

    // 从attributesKeyFrames的第一个元素获取最小等级
    const keyFrames = selectedCharacter.phases[phase].attributesKeyFrames;
    return keyFrames[0].level || 1;
  };

  // 获取该精英化阶段的最大等级
  const getMaxLevel = (phase: number) => {
    if (!selectedCharacter || !selectedCharacter.phases || !selectedCharacter.phases[phase] ||
        !selectedCharacter.phases[phase].attributesKeyFrames ||
        selectedCharacter.phases[phase].attributesKeyFrames.length === 0) {
      return 50; // 默认值
    }

    // 从attributesKeyFrames的最后一个元素获取最大等级
    const keyFrames = selectedCharacter.phases[phase].attributesKeyFrames;
    return keyFrames[keyFrames.length - 1].level || (phase === 0 ? 50 : phase === 1 ? 80 : 90);
  };

  // 获取当前可用的装备
  const getAvailableEquipment = () => {
    if (!selectedCharacter || !unlockedEquipment) {
      return [];
    }

    const dataLoader = DataLoader.getInstance();
    const availableEquipment = equipment.filter(equip =>
      dataLoader.checkEquipmentUnlocked(
        selectedCharacter.charId,
        selectedPhase,
        selectedLevel,
        equip.equipId
      )
    );

    console.log('Available equipment:', availableEquipment.map(equip => ({
      id: equip.equipId,
      name: equip.name,
      typeIcon: equip.typeIcon
    })));

    return availableEquipment;
  };

  // 获取当前选中装备的可用等级
  const getAvailableEquipmentLevels = () => {
    if (!selectedEquipment || !selectedEquipment.phases) {
      return [];
    }

    // 将equipLevel映射到更易读的名称
    const levelNameMap: Record<number, string> = {
      1: '一级模组',
      2: '二级模组',
      3: '三级模组'
    };

    return selectedEquipment.phases.map(phase => ({
      value: phase.equipLevel,
      label: levelNameMap[phase.equipLevel] || `${phase.equipLevel}级模组`
    }));
  };

  // 处理技能选择
  const handleSkillChange = (skillId: string) => {
    if (!selectedCharacter) return;

    // 找到选中的技能
    const skill = selectedCharacter.skills.find(s => s.skillId === skillId);
    if (!skill) return;

    setSelectedSkill(skill);

    // 获取技能详细信息
    const dataLoader = DataLoader.getInstance();
    const skillDetails = dataLoader.getSkillInfo(skillId);
    setSkillInfo(skillDetails);

    console.log('Selected skill:', skill);
    console.log('Skill details:', skillDetails);
  };

  // 处理技能等级选择
  const handleSkillLevelChange = (level: number) => {
    setSelectedSkillLevel(level);
  };

  // 获取可用的技能等级
  const getAvailableSkillLevels = () => {
    if (!selectedCharacter || !selectedSkill) return [];

    // 根据精英化等级确定最大技能等级
    const maxLevel = selectedPhase === 0 ? 4 : selectedPhase === 1 ? 7 : 10;

    // 生成可用等级选项
    return Array.from({ length: maxLevel }, (_, i) => i + 1).map(level => ({
      value: level,
      label: `${level}级`
    }));
  };

  // 渲染技能描述，替换描述中的标签和参数
  const renderSkillDescription = () => {
    if (!skillInfo || !selectedSkill) return null;

    // 获取当前等级的技能数据
    const levelData = skillInfo.levels[selectedSkillLevel - 1];
    if (!levelData) return null;

    return processSkillDescription(levelData.description, levelData);
  };

  // 处理技能描述，替换变量和清理富文本标记
  const processSkillDescription = (description: string, levelData: any): string => {
    if (!description) return '';

    // 先清理富文本标记
    let processedText = cleanRichTextTags(description);

    // 替换转义的换行符为实际换行符
    processedText = processedText.replace(/\\n/g, '\n');

    if (levelData) {
      // 正则表达式匹配{key:default}格式
      const variableRegex = /{([^:}]+)(:([^}]*))?}/g;

      processedText = processedText.replace(variableRegex, (match, key, _, defaultValue) => {
        // 处理duration等常规参数
        if (key === 'duration' && levelData.duration) {
          return levelData.duration.toString();
        }

        // 处理blackboard中的参数
        if (levelData.blackboard && Array.isArray(levelData.blackboard)) {
          const blackboardItem = levelData.blackboard.find((item: any) => item.key === key);

          if (blackboardItem) {
            // 根据值的类型返回正确的格式
            let value = blackboardItem.value;

            // 如果是百分比值（以%结尾）
            if (match.includes('%')) {
              // 将小数转为百分比
              return `${Math.round(value * 100)}%`;
            }

            // 使用valueStr或value
            return blackboardItem.valueStr || value.toString();
          }
        }

        // 没找到就使用默认值，如果有的话
        return defaultValue || match;
      });
    }

    return processedText;
  };

  // 清理富文本标记，移除所有格式标签
  const cleanRichTextTags = (text: string): string => {
    if (!text) return '';

    // 移除所有<@ba.xxx>内容</>格式的标签
    let cleanedText = text.replace(/<@ba\.[^>]*>([^<]*)<\/>/g, '$1');

    // 移除所有<@xxx>内容</>格式的标签
    cleanedText = cleanedText.replace(/<@[^>]*>([^<]*)<\/>/g, '$1');

    // 移除所有<$ba.xxx>格式的标签
    cleanedText = cleanedText.replace(/<\$ba\.[^>]*>/g, '');

    // 移除所有其他<$xxx>格式的标签
    cleanedText = cleanedText.replace(/<\$[^>]*>/g, '');

    // 移除所有单独的</>标签
    cleanedText = cleanedText.replace(/<\/>/g, '');

    return cleanedText;
  };

  // 处理装备描述，替换变量和清理富文本标记
  const processEquipmentDescription = (description: string, blackboard?: Array<{key: string, value: number, valueStr?: string | null}>): string => {
    if (!description) return '';

    // 先清理富文本标记
    let processedText = cleanRichTextTags(description);

    // 如果有blackboard数据，替换{key:default}格式的变量
    if (blackboard && blackboard.length > 0) {
      // 正则表达式匹配{key:default}格式
      const variableRegex = /{([^:}]+)(:([^}]*))?}/g;

      processedText = processedText.replace(variableRegex, (match, key, _, defaultValue) => {
        // 查找对应的blackboard项
        const blackboardItem = blackboard.find(item => item.key === key);

        if (blackboardItem) {
          // 根据值的类型返回正确的格式
          let value = blackboardItem.value;

          // 如果是百分比值（以%结尾）
          if (match.includes('%')) {
            // 将小数转为百分比
            return `${Math.round(value * 100)}%`;
          }

          // 使用valueStr或value
          return blackboardItem.valueStr || value.toString();
        }

        // 没找到就使用默认值，如果有的话
        return defaultValue || match;
      });
    }

    return processedText;
  };

  // 检查描述是否包含潜能加成（包含类似"+X%（+Y%）"的格式）
  const hasPotentialBonus = (description: string): boolean => {
    if (!description) return false;
    // 检查是否包含括号内的加成数据，如"（+X%）"、"（+X）"
    return /（\+\d+(\.\d+)?(%)?）/.test(description);
  };

  // 更新敌人防御
  const handleEnemyDefChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, enemyDef: value }));
  };

  // 更新敌人法抗
  const handleEnemyResChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, enemyRes: value }));
  };

  // 乘区加成处理函数
  const handleDirectAtkAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, directAtkAddition: value }));
  };

  const handleDirectAtkMultiplierChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, directAtkMultiplier: value / 100 })); // 转换百分比为小数
  };

  const handleFinalAtkAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, finalAtkAddition: value }));
  };

  const handleSkillMultiplierBonusChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, skillMultiplierBonus: value / 100 })); // 转换百分比为小数
  };

  const handleDamageAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, damageAddition: value }));
  };

  const handleFinalDamageMultiplierChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, finalDamageMultiplier: value / 100 })); // 转换百分比为小数
  };

  // 敌人双抗减免处理函数
  const handleEnemyDefReductionChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, enemyDefReduction: value }));
  };

  const handleEnemyResReductionChange = (value: number | null) => {
    if (value === null) return;
    setSkillOptions(prev => ({ ...prev, enemyResReduction: value / 100 })); // 转换百分比为小数
  };

  // 处理潜能等级变化
  const handlePotentialChange = (value: number) => {
    setSelectedPotential(value);
    // 更新当前属性以显示潜能加成
    updateCurrentAttributes(selectedPhase, selectedLevel, value);
  };

  // 格式化属性显示，显示总值和加成
  const formatAttributeWithBonus = (key: string): string => {
    if (!currentAttributes) return '';

    console.log(`Formatting attribute ${key}:`, currentAttributes[key],
                'Original value:', currentAttributes.originalValues?.[key],
                'Potential bonus:', currentAttributes.potentialBonuses?.[key]);

    const value = currentAttributes[key];
    const originalValue = currentAttributes.originalValues?.[key];
    const potentialBonus = currentAttributes.potentialBonuses?.[key];

    if (originalValue === undefined) {
      // 没有加成时，直接返回数值
      return key === 'magicResistance'
        ? value.toString()
        : Math.round(value).toString();
    }

    // 计算加成值
    const bonusValue = value - originalValue;

    console.log(`Bonus for ${key}: ${bonusValue} (${originalValue} -> ${value})`);

    // 如果加成值为0或太小，不显示加成部分
    if (Math.abs(bonusValue) < 0.01) {
      return key === 'magicResistance'
        ? value.toString()
        : Math.round(value).toString();
    }

    // 构建显示字符串
    let displayString = key === 'magicResistance'
      ? `${value.toFixed(1)}`
      : `${Math.round(value)}`;

    // 先添加潜能加成（黑色）
    if (potentialBonus && Math.abs(potentialBonus) >= 0.01) {
      if (potentialBonus > 0) {
        displayString += ` <span>(+${key === 'magicResistance' ? potentialBonus.toFixed(1) : Math.round(potentialBonus)})</span>`;
      } else {
        displayString += ` <span>(${key === 'magicResistance' ? potentialBonus.toFixed(1) : Math.round(potentialBonus)})</span>`;
      }
    }

    // 再添加装备加成（蓝色）
    const equipBonus = bonusValue - (potentialBonus || 0);
    if (Math.abs(equipBonus) >= 0.01) {
      if (equipBonus > 0) {
        displayString += ` <span style="color: #1890ff">(+${key === 'magicResistance' ? equipBonus.toFixed(1) : Math.round(equipBonus)})</span>`;
      } else {
        displayString += ` <span style="color: #1890ff">(${key === 'magicResistance' ? equipBonus.toFixed(1) : Math.round(equipBonus)})</span>`;
      }
    }

    return displayString;
  };

  // 更新技能选项回调
  const handleSkillOptionsChange = (newOptions: SkillOptions) => {
    console.log('Skill options changed:', newOptions);
    setSkillOptions(newOptions);
  };

  // 清除计算参数
  const clearCalcParams = () => {
    setSkillOptions(prev => ({
      ...prev,
      enemyDef: 0,
      enemyRes: 0,
      enemyElementalRes: 0,
      enemyDefReduction: 0,
      enemyResReduction: 0
    }));
  };

  // 清除乘区加成
  const clearMultiplyZone = () => {
    setSkillOptions(prev => ({
      ...prev,
      directAtkAddition: 0,
      directAtkMultiplier: 0,
      finalAtkAddition: 0,
      skillMultiplierBonus: 0,
      damageAddition: 0,
      finalDamageMultiplier: 0
    }));
  };

  return (
    <Card
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>干员{index + 1}</span>
          <Button
            type="text"
            danger
            onClick={onRemove}
          >
            移除
          </Button>
        </div>
      }
      style={{ marginBottom: '24px' }}
    >
      {/* 干员选择 */}
      <Card title="干员选择" style={{ marginBottom: '24px' }}>
        <Select
          style={{ width: '100%' }}
          placeholder="请选择干员"
          onChange={handleCharacterChange}
          showSearch
          optionFilterProp="children"
        >
          {characters.map(character => (
            <Option key={character.charId} value={character.charId}>
              {character?.name || '未知干员'}
            </Option>
          ))}
        </Select>
      </Card>

      {selectedCharacter && (
        <>
          {/* 干员信息 */}
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>干员信息</span>
                <Button
                  type="primary"
                  icon={operatorInfoExpanded ? <UpOutlined /> : <DownOutlined />}
                  onClick={() => setOperatorInfoExpanded(!operatorInfoExpanded)}
                >
                  {operatorInfoExpanded ? '收起详情' : '展开详情'}
                </Button>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ marginBottom: '8px' }}>精英化阶段</div>
                  <Select
                    style={{ width: '100%' }}
                    value={selectedPhase}
                    onChange={handlePhaseChange}
                  >
                    {getAvailablePhases().map(phase => (
                      <Option key={phase} value={phase}>
                        精英{phase}
                      </Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ marginBottom: '8px' }}>等级</div>
                  <InputNumber
                    style={{ width: '100%' }}
                    min={getMinLevel(selectedPhase)}
                    max={getMaxLevel(selectedPhase)}
                    value={selectedLevel}
                    onChange={handleLevelChange}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ marginBottom: '8px' }}>潜能</div>
                  {(() => {
                    console.log('Potential data:', {
                      maxPotentialLevel: selectedCharacter.maxPotentialLevel,
                      potentialRanks: selectedCharacter.potentialRanks,
                      hasPotentialRanks: Boolean(selectedCharacter.potentialRanks),
                      potentialRanksLength: selectedCharacter.potentialRanks?.length
                    });
                    return null;
                  })()}
                  <Select
                    style={{ width: '100%' }}
                    value={selectedPotential}
                    onChange={handlePotentialChange}
                    optionLabelProp="label"
                  >
                    <Option key={0} value={0} label="潜能0">
                      潜能0
                    </Option>
                    {selectedCharacter.potentialRanks && selectedCharacter.potentialRanks.length > 0 ? (
                      selectedCharacter.potentialRanks.map((potential, index) => (
                        <Option
                          key={index + 1}
                          value={index + 1}
                          label={`潜能${index + 1}`}
                        >
                          <div>
                            <div style={{ fontWeight: 'bold' }}>潜能{index + 1}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>
                              {potential.description ||
                               (potential.buff && Array.isArray(potential.buff) && potential.buff.length > 0
                                ? formatPotentialEffect(potential)
                                : "潜能效果")}
                            </div>
                          </div>
                        </Option>
                      ))
                    ) : (
                      // 没有潜能数据时，根据maxPotentialLevel生成基本的潜能选项
                      Array.from({ length: selectedCharacter.maxPotentialLevel }, (_, i) => (
                        <Option key={i + 1} value={i + 1} label={`潜能${i + 1}`}>
                          <div>
                            <div style={{ fontWeight: 'bold' }}>潜能{i + 1}</div>
                            <div style={{ fontSize: '12px', color: '#666' }}>无潜能数据</div>
                          </div>
                        </Option>
                      ))
                    )}
                  </Select>
                </div>
              </Col>
            </Row>

            {operatorInfoExpanded && (
              <Descriptions bordered style={{ marginTop: '16px' }}>
                <Descriptions.Item label="名称">{selectedCharacter.name}</Descriptions.Item>
                <Descriptions.Item label="职业">{getProfessionName(selectedCharacter.profession)}</Descriptions.Item>
                <Descriptions.Item label="子职业">{getSubProfessionName(selectedCharacter.subProfessionId)}</Descriptions.Item>
                <Descriptions.Item label="稀有度">{formatRarity(selectedCharacter.rarity)}</Descriptions.Item>
                {currentAttributes && (
                  <>
                    <Descriptions.Item label="部署费用">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('cost') }} />
                    </Descriptions.Item>
                    <Descriptions.Item label="生命值">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('maxHp') }} />
                    </Descriptions.Item>
                    <Descriptions.Item label="攻击力">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('atk') }} />
                    </Descriptions.Item>
                    <Descriptions.Item label="防御力">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('def') }} />
                    </Descriptions.Item>
                    <Descriptions.Item label="法术抗性">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('magicResistance') }} />%
                    </Descriptions.Item>
                    <Descriptions.Item label="攻击间隔">{currentAttributes.baseAttackTime}秒</Descriptions.Item>
                    <Descriptions.Item label="再部署时间">
                      <span dangerouslySetInnerHTML={{ __html: formatAttributeWithBonus('respawnTime') }} />秒
                    </Descriptions.Item>
                  </>
                )}
              </Descriptions>
            )}
          </Card>

          {/* 专属装备 */}
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>专属装备</span>
                <Button
                  type="primary"
                  icon={equipmentInfoExpanded ? <UpOutlined /> : <DownOutlined />}
                  onClick={() => setEquipmentInfoExpanded(!equipmentInfoExpanded)}
                >
                  {equipmentInfoExpanded ? '收起详情' : '展开详情'}
                </Button>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            {equipment.length > 0 ? (
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ marginBottom: '8px' }}>装备选择</div>
                    <Select
                      style={{ width: '100%' }}
                      placeholder="请选择专属装备"
                      disabled={!unlockedEquipment}
                      value={selectedEquipment?.equipId}
                      onChange={handleEquipmentChange}
                      optionLabelProp="label"
                    >
                      {getAvailableEquipment().map(equip => {
                        console.log(`Rendering equipment option: ${equip.equipId}`, {
                          name: equip.name,
                          typeIcon: equip.typeIcon
                        });

                        return (
                          <Option
                            key={equip.equipId}
                            value={equip.equipId}
                            label={equip.name}
                          >
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              {equip.typeIcon && (
                                <span style={{
                                  display: 'inline-block',
                                  marginRight: '8px',
                                  fontSize: '12px',
                                  backgroundColor: '#f5f5f5',
                                  padding: '2px 4px',
                                  borderRadius: '2px'
                                }}>
                                  {equip.typeIcon}
                                </span>
                              )}
                              <span>{equip.name || equip.equipId}</span>
                            </div>
                          </Option>
                        );
                      })}
                    </Select>
                    {!unlockedEquipment && equipment.length > 0 && (
                      <div style={{ marginTop: '8px', color: '#ff4d4f' }}>
                        需要精英{equipment[0].unlockCondition?.phase || 2}阶段{equipment[0].unlockCondition?.level || 60}级解锁
                      </div>
                    )}
                  </div>
                </Col>

                {selectedEquipment && (
                  <Col span={12}>
                    <div style={{ marginBottom: '16px' }}>
                      <div style={{ marginBottom: '8px' }}>模组等级</div>
                      <Select
                        style={{ width: '100%' }}
                        value={selectedEquipmentLevel}
                        onChange={handleEquipmentLevelChange}
                      >
                        {getAvailableEquipmentLevels().map(level => (
                          <Option key={level.value} value={level.value}>
                            {level.label}
                          </Option>
                        ))}
                      </Select>
                    </div>
                  </Col>
                )}

                {selectedEquipment && equipmentInfoExpanded && (
                  <>
                    <Col span={24}>
                      <div style={{ marginBottom: '16px' }}>
                        {(() => {
                          console.log('Rendering equipment detail:', {
                            id: selectedEquipment.equipId,
                            name: selectedEquipment.name,
                            typeIcon: selectedEquipment.typeIcon
                          });
                          return null;
                        })()}
                        <div style={{
                          fontWeight: 'bold',
                          fontSize: '16px',
                          marginBottom: '8px',
                          display: 'flex',
                          alignItems: 'center'
                        }}>
                          {selectedEquipment.typeIcon && (
                            <span style={{
                              marginRight: '10px',
                              fontSize: '14px',
                              backgroundColor: '#f5f5f5',
                              padding: '4px 8px',
                              borderRadius: '4px'
                            }}>
                              {selectedEquipment.typeIcon}
                            </span>
                          )}
                          {selectedEquipment.name || selectedEquipment.equipId}
                        </div>

                        {/* 查找当前等级的装备阶段数据，显示详细描述 */}
                        {(() => {
                          const currentPhase = selectedEquipment.phases.find(p => p.equipLevel === selectedEquipmentLevel);
                          if (!currentPhase) return null;

                          // 收集所有部件中的描述内容
                          const descriptions: React.ReactNode[] = [];

                          // 用于按天赋/特性名称分组的Map
                          const traitOverrideMap = new Map<string, {desc: string, requiredPotential: number}>();
                          const traitAdditionMap = new Map<string, {desc: string, requiredPotential: number}>();
                          const talentUpgradeMap = new Map<string, {desc: string, requiredPotential: number}>();
                          const talentDescMap = new Map<string, {desc: string, requiredPotential: number}>();

                          for (const part of currentPhase.parts || []) {
                            // 特效变更 (TraitDataBundle)
                            if (part.overrideTraitDataBundle?.candidates) {
                              part.overrideTraitDataBundle.candidates.forEach((candidate, idx) => {
                                // 获取潜能需求
                                const requiredPotential = candidate.requiredPotentialRank || 0;

                                // 只显示满足潜能需求的描述
                                if (requiredPotential > selectedPotential) return;

                                if (candidate.overrideDescripton) {
                                  const key = `trait-override-${idx}`;
                                  // 使用candidate的blackboard进行变量替换
                                  const blackboard = candidate.blackboard || [];
                                  const processedDesc = processEquipmentDescription(candidate.overrideDescripton, blackboard);

                                  // 保存当前描述，如果潜能需求更高则覆盖之前的
                                  const current = traitOverrideMap.get(key);
                                  if (!current || requiredPotential > current.requiredPotential) {
                                    traitOverrideMap.set(key, {
                                      desc: processedDesc,
                                      requiredPotential
                                    });
                                  }
                                }

                                if (candidate.additionalDescription) {
                                  const key = `trait-additional-${idx}`;
                                  // 使用candidate的blackboard进行变量替换
                                  const blackboard = candidate.blackboard || [];
                                  const processedDesc = processEquipmentDescription(candidate.additionalDescription, blackboard);

                                  // 保存当前描述，如果潜能需求更高则覆盖之前的
                                  const current = traitAdditionMap.get(key);
                                  if (!current || requiredPotential > current.requiredPotential) {
                                    traitAdditionMap.set(key, {
                                      desc: processedDesc,
                                      requiredPotential
                                    });
                                  }
                                }
                              });
                            }

                            // 天赋数据 (TalentDataBundle)
                            if (part.addOrOverrideTalentDataBundle?.candidates) {
                              part.addOrOverrideTalentDataBundle.candidates.forEach(candidate => {
                                // 获取潜能需求
                                const requiredPotential = candidate.requiredPotentialRank || 0;

                                // 只显示满足潜能需求的描述
                                if (requiredPotential > selectedPotential) return;

                                const talentKey = candidate.name || 'unknown-talent';

                                // 处理天赋加成描述
                                if (candidate.upgradeDescription) {
                                  // 使用candidate的blackboard进行变量替换
                                  const blackboard = candidate.blackboard || [];
                                  const processedDesc = processEquipmentDescription(candidate.upgradeDescription, blackboard);

                                  // 保存当前描述，如果潜能需求更高则覆盖之前的
                                  const current = talentUpgradeMap.get(talentKey);
                                  if (!current || requiredPotential > current.requiredPotential) {
                                    talentUpgradeMap.set(talentKey, {
                                      desc: processedDesc,
                                      requiredPotential
                                    });
                                  }
                                }

                                // 处理天赋基础描述
                                if (candidate.description && !candidate.isHideTalent) {
                                  // 使用candidate的blackboard进行变量替换
                                  const blackboard = candidate.blackboard || [];
                                  const processedDesc = processEquipmentDescription(candidate.description, blackboard);

                                  // 保存当前描述，如果潜能需求更高则覆盖之前的
                                  const current = talentDescMap.get(talentKey);
                                  if (!current || requiredPotential > current.requiredPotential) {
                                    talentDescMap.set(talentKey, {
                                      desc: processedDesc,
                                      requiredPotential
                                    });
                                  }
                                }
                              });
                            }
                          }

                          // 添加特性替换描述
                          traitOverrideMap.forEach((data, key) => {
                            descriptions.push(
                              <div key={key} style={{ margin: '8px 0' }}>
                                <div style={{ fontWeight: 'bold' }}>特性替换:</div>
                                <div>{data.desc}</div>
                              </div>
                            );
                          });

                          // 添加特性追加描述
                          traitAdditionMap.forEach((data, key) => {
                            descriptions.push(
                              <div key={key} style={{ margin: '8px 0' }}>
                                <div style={{ fontWeight: 'bold' }}>特性追加:</div>
                                <div>{data.desc}</div>
                              </div>
                            );
                          });

                          // 添加天赋加成描述
                          talentUpgradeMap.forEach((data, talentName) => {
                            descriptions.push(
                              <div key={`talent-upgrade-${talentName}`} style={{ margin: '8px 0' }}>
                                <div style={{ fontWeight: 'bold' }}>
                                  {talentName !== 'unknown-talent' ? `天赋"${talentName}"加成:` : '天赋加成:'}
                                </div>
                                <div>{data.desc}</div>
                              </div>
                            );
                          });

                          // 添加天赋基础描述
                          talentDescMap.forEach((data, talentName) => {
                            descriptions.push(
                              <div key={`talent-desc-${talentName}`} style={{ margin: '8px 0' }}>
                                <div style={{ fontWeight: 'bold' }}>
                                  {talentName !== 'unknown-talent' ? `天赋"${talentName}":` : '天赋:'}
                                </div>
                                <div>{data.desc}</div>
                              </div>
                            );
                          });

                          return descriptions.length > 0 ? (
                            <div style={{
                              border: '1px solid #d9d9d9',
                              borderRadius: '4px',
                              padding: '12px',
                              marginTop: '8px',
                              backgroundColor: '#fafafa'
                            }}>
                              {descriptions}
                            </div>
                          ) : null;
                        })()}
                      </div>
                    </Col>

                    <Col span={24}>
                      <div style={{ marginTop: '8px' }}>
                        <div style={{ marginBottom: '8px' }}>属性加成</div>
                        <ul style={{ paddingLeft: '20px' }}>
                          {selectedEquipment.phases
                            .find(p => p.equipLevel === selectedEquipmentLevel)
                            ?.attributeBlackboard.map((attr, index) => {
                              // 转换属性名称为更易读的格式
                              const attrNameMap: Record<string, string> = {
                                'max_hp': '生命值',
                                'atk': '攻击力',
                                'def': '防御力',
                                'attack_speed': '攻击速度',
                                'magic_resistance': '法术抗性',
                                'cost': '部署费用',
                                'block_cnt': '阻挡数',
                                'respawn_time': '再部署时间'
                              };
                              const displayName = attrNameMap[attr.key] || attr.key;
                              return (
                                <li key={index} style={{ color: '#1890ff' }}>
                                  {displayName}: +{attr.value}
                                </li>
                              );
                            })}
                        </ul>
                      </div>
                    </Col>
                  </>
                )}
              </Row>
            ) : (
              <Alert message="该干员暂无专属装备数据" type="info" showIcon />
            )}
          </Card>

          {/* 技能选择 */}
          <Card
            title={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>技能选择</span>
                <Button
                  type="primary"
                  icon={skillInfoExpanded ? <UpOutlined /> : <DownOutlined />}
                  onClick={() => setSkillInfoExpanded(!skillInfoExpanded)}
                >
                  {skillInfoExpanded ? '收起详情' : '展开详情'}
                </Button>
              </div>
            }
            style={{ marginBottom: '24px' }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div style={{ marginBottom: '16px' }}>
                  <div style={{ marginBottom: '8px' }}>技能</div>
                  <Select
                    style={{ width: '100%' }}
                    placeholder="请选择技能"
                    value={selectedSkill?.skillId}
                    onChange={handleSkillChange}
                    optionLabelProp="label"
                  >
                    {selectedCharacter.skills.map((skill, index) => {
                      // 获取技能详细信息
                      const dataLoader = DataLoader.getInstance();
                      const skillDetail = dataLoader.getSkillInfo(skill.skillId);
                      // 优先使用skillDetail中的名称，然后是skill.name，最后是默认名称
                      const skillName = (skillDetail && skillDetail.levels && skillDetail.levels[0]?.name) ||
                                         skill.name ||
                                         `技能${index + 1}`;
                      return (
                        <Option
                          key={skill.skillId}
                          value={skill.skillId}
                          label={`技能${index + 1}：${skillName}`}
                        >
                          <div style={{ fontWeight: 'bold' }}>技能{index + 1}：{skillName}</div>
                        </Option>
                      );
                    })}
                  </Select>
                </div>
              </Col>

              {selectedSkill && (
                <Col span={12}>
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ marginBottom: '8px' }}>技能等级</div>
                    <Select
                      style={{ width: '100%' }}
                      value={selectedSkillLevel}
                      onChange={handleSkillLevelChange}
                    >
                      {getAvailableSkillLevels().map(level => (
                        <Option key={level.value} value={level.value}>
                          {level.label}
                        </Option>
                      ))}
                    </Select>
                  </div>
                </Col>
              )}

              {selectedSkill && skillInfo && skillInfoExpanded && (
                <Col span={24}>
                  <div style={{
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    padding: '12px',
                    backgroundColor: '#fafafa',
                    marginTop: '8px'
                  }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>
                      {skillInfo.levels[selectedSkillLevel - 1]?.name || selectedSkill.name}
                    </div>
                    <pre style={{
                      margin: 0,
                      whiteSpace: 'pre-wrap',
                      wordWrap: 'break-word',
                      fontFamily: 'inherit',
                      fontSize: 'inherit',
                      background: 'transparent'
                    }}>
                      {renderSkillDescription()}
                    </pre>
                  </div>
                </Col>
              )}

              {/* 技能基本信息 */}
              {selectedSkill && skillInfo && skillInfoExpanded && (
                <Col span={24} style={{ marginTop: '16px' }}>
                  <Card type="inner" title="技能基本信息">
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <div className="skill-info-item">
                          <div className="skill-info-label">SP回复类型</div>
                          <div className="skill-info-value">
                            {(() => {
                              const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                              if (!currentLevelInfo || !currentLevelInfo.spData || !currentLevelInfo.spData.spType) {
                                return selectedSkill.spData?.spType ? SP_TYPE_MAP[selectedSkill.spData.spType] || "未知回复类型" : "未知回复类型";
                              }
                              return SP_TYPE_MAP[currentLevelInfo.spData.spType] || currentLevelInfo.spData.spType || "未知回复类型";
                            })()}
                          </div>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="skill-info-item">
                          <div className="skill-info-label">技能释放类型</div>
                          <div className="skill-info-value">
                            {(() => {
                              const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                              if (!currentLevelInfo || !currentLevelInfo.skillType) {
                                return "未知技能类型";
                              }
                              return SKILL_TYPE_MAP[currentLevelInfo.skillType] || currentLevelInfo.skillType;
                            })()}
                          </div>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="skill-info-item">
                          <div className="skill-info-label">持续类型</div>
                          <div className="skill-info-value">
                            {(() => {
                              const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                              if (!currentLevelInfo || !currentLevelInfo.durationType) {
                                return "未知持续类型";
                              }
                              return DURATION_TYPE_MAP[currentLevelInfo.durationType] || currentLevelInfo.durationType;
                            })()}
                          </div>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="skill-info-item">
                          <div className="skill-info-label">初始SP</div>
                          <div className="skill-info-value">
                            {(() => {
                              const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                              const spData = currentLevelInfo?.spData || selectedSkill.spData;
                              return spData ? spData.initSp : "未知";
                            })()}
                          </div>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div className="skill-info-item">
                          <div className="skill-info-label">SP需求</div>
                          <div className="skill-info-value">
                            {(() => {
                              const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                              const spData = currentLevelInfo?.spData || selectedSkill.spData;
                              return spData ? spData.spCost : "未知";
                            })()}
                          </div>
                        </div>
                      </Col>
                      {(() => {
                        const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                        const skillDuration = currentLevelInfo?.duration !== undefined ?
                          currentLevelInfo.duration : selectedSkill.duration;
                        const isAmmoType = currentLevelInfo?.durationType === "AMMO";

                        if (skillDuration > 0 && !isAmmoType) {
                          return (
                            <Col span={8}>
                              <div className="skill-info-item">
                                <div className="skill-info-label">技能持续时间</div>
                                <div className="skill-info-value">{skillDuration} 秒</div>
                              </div>
                            </Col>
                          );
                        }
                        return null;
                      })()}
                      {(() => {
                        const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                        const isAmmoType = currentLevelInfo?.durationType === "AMMO";

                        if (isAmmoType) {
                          // 查找弹药相关参数
                          let maxAmmo: number | null = null;
                          if (currentLevelInfo && currentLevelInfo.blackboard) {
                            // 首先检查特定格式的键名
                            const ammoData = currentLevelInfo.blackboard.find(
                              (item: any) =>
                                item.key?.includes("trigger_time") ||
                                item.key?.includes("count") ||
                                item.key?.includes("max_ammo") ||
                                item.key?.includes("ammo")
                            );

                            if (ammoData) {
                              maxAmmo = ammoData.value;
                            } else if (currentLevelInfo.description) {
                              // 如果没有找到，尝试解析技能描述中的弹药数量
                              const ammoMatch = currentLevelInfo.description.match(/(\d+)发弹药/);
                              if (ammoMatch && ammoMatch[1]) {
                                maxAmmo = parseInt(ammoMatch[1], 10);
                              }
                            }
                          }

                          if (maxAmmo !== null) {
                            return (
                              <Col span={8}>
                                <div className="skill-info-item">
                                  <div className="skill-info-label">最大弹药量</div>
                                  <div className="skill-info-value">{maxAmmo}</div>
                                </div>
                              </Col>
                            );
                          }
                        }
                        return null;
                      })()}
                      {(() => {
                        const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                        if (currentLevelInfo && currentLevelInfo.blackboard) {
                          // 查找攻击力倍率相关参数
                          const atkScaleData = currentLevelInfo.blackboard.find(
                            (item: any) =>
                              item.key?.includes("atk_scale") ||
                              item.key?.includes("attack_scale") ||
                              item.key?.includes("attack@atk_scale") ||
                              item.key?.includes("damage_scale")
                          );

                          if (atkScaleData) {
                            const atkScale = (atkScaleData.value * 100).toFixed(0) + '%';
                            return (
                              <Col span={8}>
                                <div className="skill-info-item">
                                  <div className="skill-info-label">攻击力倍率</div>
                                  <div className="skill-info-value">{atkScale}</div>
                                </div>
                              </Col>
                            );
                          }
                        }
                        return null;
                      })()}
                      {(() => {
                        const currentLevelInfo = skillInfo.levels[selectedSkillLevel - 1];
                        if (currentLevelInfo?.spData?.maxChargeTime > 1) {
                          return (
                            <Col span={8}>
                              <div className="skill-info-item">
                                <div className="skill-info-label">最大充能次数</div>
                                <div className="skill-info-value">{currentLevelInfo.spData.maxChargeTime}</div>
                              </div>
                            </Col>
                          );
                        }
                        return null;
                      })()}
                    </Row>
                  </Card>
                </Col>
              )}
            </Row>
          </Card>

          {/* 技能计算结果 */}
          {selectedSkill && selectedCharacter && currentAttributes && (
            <SkillResultPanel
              character={{
                ...selectedCharacter,
                calculatedAttributes: {
                  atk: currentAttributes.atk,
                  baseAttackTime: currentAttributes.baseAttackTime,
                  defense: currentAttributes.def,
                  maxHp: currentAttributes.maxHp
                }
              }}
              skill={selectedSkill}
              skillLevel={selectedSkillLevel}
              options={skillOptions}
              onOptionsChange={handleLocalOptionsChange}
            />
          )}
        </>
      )}
    </Card>
  );
};

const DpsCalculator: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [characters, setCharacters] = useState<CharacterData[]>([]);
  const [operatorIndices, setOperatorIndices] = useState<number[]>([0]); // Track operator indices
  const [calcParamsExpanded, setCalcParamsExpanded] = useState<boolean>(false);
  const [multiplyZoneExpanded, setMultiplyZoneExpanded] = useState<boolean>(false);

  // 共享的计算参数和乘区加成状态
  const [sharedOptions, setSharedOptions] = useState<SkillOptions>({
    enemyDef: 0,
    enemyRes: 0,
    enemyElementalRes: 0,
    potentialRank: 0,
    moduleLevel: 0,
    directAtkAddition: 0,
    directAtkMultiplier: 0,
    finalAtkAddition: 0,
    skillMultiplierBonus: 0,
    damageAddition: 0,
    finalDamageMultiplier: 0,
    enemyDefReduction: 0,
    enemyResReduction: 0
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        const dataLoader = DataLoader.getInstance();
        await dataLoader.loadData();
        const characterList = dataLoader.getCharacterList();
        setCharacters(characterList);
        setLoading(false);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('数据加载失败，请刷新页面重试');
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handleAddOperator = () => {
    if (operatorIndices.length < 4) {
      // Generate a new unique index
      const newIndex = Math.max(...operatorIndices, 0) + 1;
      setOperatorIndices(prev => [...prev, newIndex]);
    }
  };

  const handleRemoveOperator = (index: number) => {
    if (operatorIndices.length > 1) {
      // Remove the specific operator by its index
      setOperatorIndices(prev => prev.filter(i => i !== index));
    }
  };

  // 清除所有计算参数
  const clearCalcParams = () => {
    setSharedOptions(prev => ({
      ...prev,
      enemyDef: 0,
      enemyRes: 0,
      enemyElementalRes: 0,
      enemyDefReduction: 0,
      enemyResReduction: 0
    }));
  };

  // 清除所有乘区加成
  const clearMultiplyZone = () => {
    setSharedOptions(prev => ({
      ...prev,
      directAtkAddition: 0,
      directAtkMultiplier: 0,
      finalAtkAddition: 0,
      skillMultiplierBonus: 0,
      damageAddition: 0,
      finalDamageMultiplier: 0
    }));
  };

  // 处理计算参数变化
  const handleEnemyDefChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, enemyDef: value }));
  };

  const handleEnemyResChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, enemyRes: value }));
  };

  const handleEnemyDefReductionChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, enemyDefReduction: value }));
  };

  const handleEnemyResReductionChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, enemyResReduction: value / 100 }));
  };

  // 处理乘区加成变化
  const handleDirectAtkAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, directAtkAddition: value }));
  };

  const handleDirectAtkMultiplierChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, directAtkMultiplier: value / 100 }));
  };

  const handleFinalAtkAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, finalAtkAddition: value }));
  };

  const handleSkillMultiplierBonusChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, skillMultiplierBonus: value / 100 }));
  };

  const handleDamageAdditionChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, damageAddition: value }));
  };

  const handleFinalDamageMultiplierChange = (value: number | null) => {
    if (value === null) return;
    setSharedOptions(prev => ({ ...prev, finalDamageMultiplier: value / 100 }));
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert message="错误" description={error} type="error" showIcon />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>DPS 计算器</Title>

      {/* 干员卡片容器 */}
      <Row gutter={[24, 24]}>
        {operatorIndices.map((opIndex, idx) => (
          <Col span={24 / operatorIndices.length} key={opIndex}>
            <OperatorCard
              index={idx}
              onRemove={() => handleRemoveOperator(opIndex)}
              characters={characters}
              sharedOptions={sharedOptions}
              onOptionsChange={setSharedOptions}
            />
          </Col>
        ))}
      </Row>

      {/* 新增干员按钮 */}
      {operatorIndices.length < 4 && (
        <div style={{ marginBottom: '24px', textAlign: 'center' }}>
          <Button type="primary" onClick={handleAddOperator}>
            新增干员对比
          </Button>
        </div>
      )}

      {/* 计算参数卡片 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>计算参数</span>
            <div>
              <Button
                icon={<ClearOutlined />}
                style={{ marginRight: '8px' }}
                onClick={clearCalcParams}
              >
                清除
              </Button>
              <Button
                type="text"
                icon={calcParamsExpanded ? <UpOutlined /> : <DownOutlined />}
                onClick={() => setCalcParamsExpanded(!calcParamsExpanded)}
              >
                {calcParamsExpanded ? '收起' : '展开'}
              </Button>
            </div>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        {calcParamsExpanded && (
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>敌人防御</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  value={sharedOptions.enemyDef}
                  onChange={handleEnemyDefChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>敌人法抗(%)</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  value={sharedOptions.enemyRes}
                  onChange={handleEnemyResChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>敌人防御力减免</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  value={sharedOptions.enemyDefReduction}
                  onChange={handleEnemyDefReductionChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>敌人法抗减免(%)</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  value={sharedOptions.enemyResReduction ? sharedOptions.enemyResReduction * 100 : 0}
                  onChange={handleEnemyResReductionChange}
                />
              </div>
            </Col>
          </Row>
        )}
      </Card>

      {/* 乘区加成卡片 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>乘区加成</span>
            <div>
              <Button
                icon={<ClearOutlined />}
                style={{ marginRight: '8px' }}
                onClick={clearMultiplyZone}
              >
                清除
              </Button>
              <Button
                type="text"
                icon={multiplyZoneExpanded ? <UpOutlined /> : <DownOutlined />}
                onClick={() => setMultiplyZoneExpanded(!multiplyZoneExpanded)}
              >
                {multiplyZoneExpanded ? '收起' : '展开'}
              </Button>
            </div>
          </div>
        }
        style={{ marginBottom: '24px' }}
      >
        {multiplyZoneExpanded && (
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>A区: 攻击力直接加算</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  value={sharedOptions.directAtkAddition}
                  onChange={handleDirectAtkAdditionChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>B区: 攻击力直接乘算(%)</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={500}
                  value={sharedOptions.directAtkMultiplier ? sharedOptions.directAtkMultiplier * 100 : 0}
                  onChange={handleDirectAtkMultiplierChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>C区: 攻击力最终加算</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  value={sharedOptions.finalAtkAddition}
                  onChange={handleFinalAtkAdditionChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>D区: 技能倍率额外加成(%)</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={500}
                  value={sharedOptions.skillMultiplierBonus ? sharedOptions.skillMultiplierBonus * 100 : 0}
                  onChange={handleSkillMultiplierBonusChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>E区: 伤害加算</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={9999}
                  value={sharedOptions.damageAddition}
                  onChange={handleDamageAdditionChange}
                />
              </div>
            </Col>

            <Col span={12}>
              <div style={{ marginBottom: '16px' }}>
                <div style={{ marginBottom: '8px' }}>F区: 伤害最终乘算(%)</div>
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={500}
                  value={sharedOptions.finalDamageMultiplier ? sharedOptions.finalDamageMultiplier * 100 : 0}
                  onChange={handleFinalDamageMultiplierChange}
                />
              </div>
            </Col>
          </Row>
        )}
      </Card>
    </div>
  );
};

export default DpsCalculator;