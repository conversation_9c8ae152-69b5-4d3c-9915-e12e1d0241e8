import { SkillCalculator, SkillOptions } from "../types/skill";
import { CharacterData, CharacterSkill } from "../types/character";
// 导入默认计算器
import { DefaultSkillCalculator } from "./skillCalculators/DefaultSkillCalculator";

// 工厂类来创建合适的技能计算器
class SkillCalculatorFactory {
    // 缓存已加载的计算器
    private static loadedCalculators: Record<string, any> = {};
    
    static async createCalculatorAsync(
      character: CharacterData,
      skill: CharacterSkill,
      skillLevel: number,
      options: SkillOptions
    ): Promise<SkillCalculator> {
      const key = `${character.charId}_${skill.skillId}`;
      console.log(`尝试加载计算器: ${key}`);
      console.log(`角色ID: ${character.charId}, 技能ID: ${skill.skillId}`);
      
      try {
        // 1. 检查是否已缓存
        if (this.loadedCalculators[key]) {
          console.log(`使用缓存计算器: ${key}`);
          return new this.loadedCalculators[key](character, skill, skillLevel, options);
        }
        
        // 2. 尝试按命名约定动态导入
        try {
          // 提取干员编号和技能编号
          const charMatch = character.charId.match(/char_(\d+)_(\w+)/);
          if (charMatch) {
            const [_, charNum, charName] = charMatch;
            console.log(`提取到干员编号: ${charNum}, 干员名称: ${charName}`);
            
            // 尝试从技能ID提取技能编号
            let skillNum = '1';
            const skillMatch = skill.skillId.match(/skchr_.+_(\d+)/);
            if (skillMatch && skillMatch[1]) {
              skillNum = skillMatch[1];
            }
            console.log(`提取到技能编号: ${skillNum}`);
            
            // 构造类名，如"AngelinaS3Calculator"
            const capitalizedCharName = charName.charAt(0).toUpperCase() + charName.slice(1);
            const className = `${capitalizedCharName}S${skillNum}Calculator`;
            
            // 构造文件路径
            const modulePath = `./skillCalculators/${className}`;
            
            console.log(`尝试动态加载计算器: ${modulePath}, 类名: ${className}`);
            
            try {
              // 动态导入
              const module = await import(/* @vite-ignore */ /* webpackChunkName: "[request]" */ modulePath);
              console.log(`模块导入成功, 模块内容: ${Object.keys(module).join(', ')}`);
              
              const exportedClass = module.default || module[className]; 
              if (exportedClass) {
                console.log(`找到导出的类: ${className}`);
                this.loadedCalculators[key] = exportedClass;
                return new exportedClass(character, skill, skillLevel, options);
              } else {
                console.log(`未找到导出的类: ${className}，可用导出: ${Object.keys(module).join(', ')}`);
              }
            } catch (importError) {
              console.error(`导入模块出错: ${modulePath}`, importError);
            }
          } else {
            console.log(`无法从角色ID解析出名称: ${character.charId}`);
          }
        } catch (innerError) {
          console.error(`处理专用计算器过程中出错: ${key}`, innerError);
        }
        
        // 3. 使用默认计算器
        console.log(`使用默认计算器`);
        return new DefaultSkillCalculator(character, skill, skillLevel, options);
      } catch (error) {
        console.error(`创建技能计算器出错: ${error}`);
        return new DefaultSkillCalculator(character, skill, skillLevel, options);
      }
    }
    
    // 同步方法 - 用于向后兼容
    static createCalculator(
      character: CharacterData,
      skill: CharacterSkill,
      skillLevel: number,
      options: SkillOptions
    ): SkillCalculator {
      // 返回默认计算器，异步获取的结果将在稍后更新UI
      const calculator = new DefaultSkillCalculator(character, skill, skillLevel, options);
      
      // 尝试异步加载，成功后触发UI更新 
      this.createCalculatorAsync(character, skill, skillLevel, options)
        .then(asyncCalculator => {
          // 这里可以添加通知UI更新的逻辑
          console.log(`异步加载计算器成功: ${character.charId}_${skill.skillId}`);
        })
        .catch(error => console.error(`异步加载计算器失败: ${character.charId}_${skill.skillId}`, error));
      
      return calculator;
    }
  }

export default SkillCalculatorFactory;