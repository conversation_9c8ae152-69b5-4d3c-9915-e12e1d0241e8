export interface CharacterSkill {
  skillId: string;
  name: string;
  description: string;
  spData: {
    spType: number;
    initSp: number;
    maxSp: number;
  };
  duration: number;
  blackboard: Array<{
    key: string;
    value: number;
  }>;
}

export interface CharacterTalent {
  talentId: string;
  name: string;
  description: string;
  phase: number;
  level: number;
  potential: number;
}

export interface CharacterData {
  charId: string;
  name: string;
  description: string;
  profession: string;
  subProfessionId: string;
  position: string;
  rarity: number;
  maxLevel: number;
  maxPotentialLevel: number;
  attributes: {
    maxHp: number;
    atk: number;
    def: number;
    magicResistance: number;
    cost: number;
    blockCnt: number;
    moveSpeed: number;
    attackSpeed: number;
    baseAttackTime: number;
    respawnTime: number;
    hpRecoveryPerSec: number;
    spRecoveryPerSec: number;
    maxDeployCount: number;
    maxDeckStackCnt: number;
    tauntLevel: number;
    massLevel: number;
    baseForceLevel: number;
    stunImmune: boolean;
    silenceImmune: boolean;
    sleepImmune: boolean;
  };
  talents: CharacterTalent[];
  skills: CharacterSkill[];
  phases: any[];
  potentialRanks?: {
    buff?: Array<{
      type: string;
      key: string;
      value: number;
    }>;
    description?: string;
  }[];
  calculatedAttributes?: {
    atk?: number;
    baseAttackTime?: number;
    defense?: number;
    maxHp?: number;
    // ... 可能的其他计算属性
  };
} 