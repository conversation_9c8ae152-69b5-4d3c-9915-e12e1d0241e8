/**
 * 潜能处理助手
 * 处理明日方舟中的潜能数据和潜能加成计算
 */

// 属性类型映射
const ATTRIBUTE_TYPE_MAP: Record<string, string> = {
  'MAX_HP': 'maxHp',
  'ATK': 'atk',
  'DEF': 'def',
  'ATTACK_SPEED': 'attackSpeed',
  'MAGIC_RESISTANCE': 'magicResistance',
  'COST': 'cost',
  'BLOCK_CNT': 'blockCnt',
  'RESPAWN_TIME': 'respawnTime',
  'SP_RECOVERY_PER_SEC': 'spRecoveryPerSec'
};

// 属性类型显示名称映射
const ATTRIBUTE_DISPLAY_MAP: Record<string, string> = {
  'MAX_HP': '生命上限',
  'ATK': '攻击力',
  'DEF': '防御力',
  'ATTACK_SPEED': '攻击速度',
  'MAGIC_RESISTANCE': '法术抗性',
  'COST': '部署费用',
  'BLOCK_CNT': '阻挡数',
  'RESPAWN_TIME': '再部署时间',
  'SP_RECOVERY_PER_SEC': '技力回复'
};

/**
 * 获取人类可读的潜能描述
 */
export function formatPotentialEffect(potential: any): string {
  // 如果有描述，直接返回
  if (potential.description) {
    return potential.description;
  }
  
  // 如果不是BUFF类型或buff不存在，返回默认文本
  if (potential.type !== 'BUFF' || 
      !potential.buff || 
      !potential.buff.attributes || 
      !potential.buff.attributes.attributeModifiers || 
      !Array.isArray(potential.buff.attributes.attributeModifiers) ||
      potential.buff.attributes.attributeModifiers.length === 0) {
    return "潜能效果";
  }
  
  const results: string[] = [];
  
  // 处理attributeModifiers中的属性修改
  const modifiers = potential.buff.attributes.attributeModifiers;
  
  for (const modifier of modifiers) {
    // 获取属性类型和值
    const attrType = modifier.attributeType;
    const value = modifier.value;
    
    // 格式化显示
    if (attrType && value !== undefined && value !== null) {
      const displayName = ATTRIBUTE_DISPLAY_MAP[attrType] || attrType;
      const sign = value >= 0 ? '+' : '';
      results.push(`${displayName} ${sign}${value}`);
    }
  }
  
  // 如果没有找到任何效果
  if (results.length === 0) {
    return "潜能效果";
  }
  
  return results.join('，');
}

/**
 * 应用潜能加成到属性上
 */
export function applyPotentialBonus(character: any, attributes: any, potentialLevel: number): any {
  if (!character || !character.potentialRanks || potentialLevel <= 0) {
    return attributes;
  }
  
  // 创建一个新对象，避免修改原始对象
  const result = JSON.parse(JSON.stringify(attributes));
  
  // 确保originalValues存在，用于记录原始值
  if (!result.originalValues) {
    result.originalValues = {};
    // 记录所有属性的原始值
    for (const key in result) {
      if (typeof result[key] === 'number' && key !== 'originalValues') {
        result.originalValues[key] = result[key];
      }
    }
  }
  
  // 确保potentialBonuses存在，用于记录潜能加成
  result.potentialBonuses = result.potentialBonuses || {};
  
  // 应用所有小于等于当前潜能等级的潜能效果
  for (let i = 0; i < potentialLevel && i < character.potentialRanks.length; i++) {
    const potential = character.potentialRanks[i];
    
    // 检查是否是BUFF类型潜能，并检查attributeModifiers是否存在
    if (potential.type !== 'BUFF' || 
        !potential.buff || 
        !potential.buff.attributes || 
        !potential.buff.attributes.attributeModifiers ||
        !Array.isArray(potential.buff.attributes.attributeModifiers)) {
      continue;
    }
    
    // 获取属性修改列表
    const modifiers = potential.buff.attributes.attributeModifiers;
    
    // 应用每个属性修改
    for (const modifier of modifiers) {
      const attrType = modifier.attributeType;
      const formula = modifier.formulaItem;
      const value = modifier.value;
      
      // 跳过无效的属性修改
      if (!attrType || value === undefined || value === null) {
        continue;
      }
      
      // 映射属性键
      const attrKey = ATTRIBUTE_TYPE_MAP[attrType];
      if (!attrKey || result[attrKey] === undefined) {
        console.log(`跳过未知属性: ${attrType}`);
        continue;
      }
      
      // 根据公式应用不同的加成方式
      if (formula === 'ADDITION') {
        // 直接加法
        console.log(`应用潜能${i+1}加成: ${attrKey} ${value >= 0 ? '+' : ''}${value}`);
        result[attrKey] += value;
        result.potentialBonuses[attrKey] = (result.potentialBonuses[attrKey] || 0) + value;
      } else if (formula === 'MULTIPLY') {
        // 乘法
        const addValue = result.originalValues[attrKey] * value;
        console.log(`应用潜能${i+1}加成: ${attrKey} *${value} (${addValue})`);
        result[attrKey] += addValue;
        result.potentialBonuses[attrKey] = (result.potentialBonuses[attrKey] || 0) + addValue;
      } else {
        console.log(`未知公式类型: ${formula}`);
      }
    }
  }
  
  return result;
} 