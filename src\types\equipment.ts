export interface EquipmentAttribute {
  key: string;
  value: number;
}

export interface EquipmentUnlockCondition {
  phase: number;
  level: number;
}

export interface EquipmentPhase {
  equipLevel: number;
  parts: any[];
  attributeBlackboard: EquipmentAttribute[];
  tokenAttributeBlackboard: Record<string, EquipmentAttribute[]>;
}

export interface EquipmentData {
  equipId: string;
  charId: string;
  name: string;
  description: string;
  typeIcon: string;
  phases: EquipmentPhase[];
  unlockCondition?: EquipmentUnlockCondition;
} 