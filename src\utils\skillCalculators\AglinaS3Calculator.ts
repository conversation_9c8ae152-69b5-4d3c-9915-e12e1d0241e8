import { SkillCalculator } from "../../types/skill";
import { DamageType } from "../../utils/SkillCalculator";
import { CharacterData, CharacterSkill } from "../../types/character";
import { SkillOptions } from "../../types/skill";

/**
 * 安洁莉娜S3计算器 - 使用简化的基类实现，并添加了S3技能的计算
 * 全场所有敌人失重，攻击范围扩大，攻击力+75%，可以攻击5个敌人\n技能未开启时无法普通攻击
 * 需要从角色数据（算上了潜能与模组的总计）中获取角色属性，并计算技能伤害
 */

export class AglinaS3Calculator extends SkillCalculator {
    constructor(
        characterData: CharacterData, 
        skill: CharacterSkill,
        skillLevel: number,
        options: SkillOptions
    ) {
        super(characterData, skill, skillLevel, options);
        
        // 构造函数中打印调试信息
        console.log("AglinaS3Calculator 被成功实例化");
        console.log("干员信息:", JSON.stringify({
            charId: characterData.charId,
            name: characterData.name,
            skillId: skill.skillId,
            skillName: skill.name
        }));
    }
    
    // 指定安洁莉娜S3的伤害类型为法术伤害
    protected getDamageType(): DamageType {
        return DamageType.MAGICAL;
    }
    
    // 从技能blackboard中获取atk参数(攻击力加成)
    protected getDirectAtkMultiplier(): number {
        // 从技能的blackboard中查找key为"atk"的项
        if (!this.skill?.blackboard) {
            return 0.181; // 如果blackboard不存在，返回默认值
        }
        const atkBuff = this.skill.blackboard.find(item => item.key === "atk");
        //在控制台输出
        console.log("atkBuff", atkBuff);
        // 如果找到，返回对应的值；否则使用默认值0.75（75%攻击力加成）
        return atkBuff ? atkBuff.value : 0.75;
    }
    
    // 获取自定义增益
    protected getCustomBuffs() {
        return this.options.customBuffs || {};
    }
    
    // 应用自定义增益到攻击力
    protected applyCustomBuffsToAttack(baseAttack: number): number {
        const buffs = this.getCustomBuffs();
        let result = baseAttack;
        
        // 攻击力直接加算
        if (buffs.attackFirstValue) {
            result += buffs.attackFirstValue;
        }
        
        // 攻击力直接乘算 (百分比)
        if (buffs.attackFirstRatio) {
            result *= (1 + buffs.attackFirstRatio / 100);
        }
        
        // 攻击力最终加算
        if (buffs.attackFinalValue) {
            result += buffs.attackFinalValue;
        }
        
        return result;
    }
    
    // 应用自定义增益到伤害
    protected applyCustomBuffsToDamage(damage: number): number {
        const buffs = this.getCustomBuffs();
        let result = damage;
        
        // 通用伤害增幅
        if (buffs.damageAmplifyRatio) {
            result *= (1 + buffs.damageAmplifyRatio / 100);
        }
        
        // 法术伤害增幅
        if (buffs.magicalDamageAmplifyRatio) {
            result *= (1 + buffs.magicalDamageAmplifyRatio / 100);
        }
        
        // 敌人脆弱状态
        if (buffs.enemyVulnerableRatio) {
            result *= (1 + buffs.enemyVulnerableRatio / 100);
        }
        
        // 敌人法术脆弱
        if (buffs.enemyVulnerableMagicalRatio) {
            result *= (1 + buffs.enemyVulnerableMagicalRatio / 100);
        }
        
        return result;
    }
    
    // 获取角色基础信息（测试输出用）
    getCharacterInfo(): string {
        return JSON.stringify({
            name: "安洁莉娜",
            baseAtk: this.character.attributes.atk,
            attackSpeed: this.character.attributes.attackSpeed,
            baseAttackTime: this.character.attributes.baseAttackTime,
            skillLevel: this.skillLevel,
            skillName: "技能3：重力源",
            atkMultiplier: this.getDirectAtkMultiplier()
        });
    }
    
    // 获取DPS输出
    getDps(): string {
        // 基础攻击力
        let baseAtk = this.character.attributes.atk || 600; // 默认值
        
        // 技能攻击力加成
        const atkMultiplier = this.getDirectAtkMultiplier();
        //控制台输出atkMultiplier
        console.log("atkMultiplier", atkMultiplier);
        
        // 应用技能加成
        let skillBoostAtk = baseAtk * (1 + atkMultiplier);
        
        // 应用自定义增益
        skillBoostAtk = this.applyCustomBuffsToAttack(skillBoostAtk);
        
        // 敌人法抗
        const enemyRes = this.options.enemyRes || 0;
        const actualRes = Math.max(0, enemyRes - (this.getCustomBuffs().enemyResLoss || 0));
        
        // 计算基础伤害
        let damage = skillBoostAtk * Math.max(1 - actualRes / 100, 0.05);
        
        // 应用自定义增益到伤害
        damage = this.applyCustomBuffsToDamage(damage);
        
        // 计算DPS (安洁莉娜S3的攻击间隔是1.9秒)
        const attackInterval = 1.9;
        const dps = damage / attackInterval;
        
        // 返回格式化的DPS值
        return dps.toFixed(2);
    }
    
    // 获取平均DPS（考虑技能持续时间与冷却时间）
    getAverageDps(): string {
        // 获取技能持续时间
        const duration = this.skill.duration || 25; // 默认25秒
        
        // 获取技能SP
        const spData = this.skill.spData || { spType: 1, maxSp: 25, initSp: 0 };
        
        // 计算技能循环时间（持续时间 + 冷却时间）
        const cooldown = spData.maxSp;
        const totalCycle = duration + cooldown;
        
        // 计算单次技能期间的总伤害
        const dps = parseFloat(this.getDps());
        const skillDamage = dps * duration;
        
        // 计算平均DPS
        const averageDps = skillDamage / totalCycle;
        
        return averageDps.toFixed(2);
    }
    
    // 获取技能总伤害
    getTotalDamage(): string {
        // 基础DPS
        const dps = parseFloat(this.getDps());
        
        // 技能持续时间
        const duration = this.skill.duration || 25; // 默认25秒
        
        // 计算总伤害
        const totalDamage = dps * duration;
        
        return Math.round(totalDamage).toString();
    }
    
    // 获取技能持续时间信息
    getSkillDurationInfo(): string {
        return JSON.stringify({
            duration: this.skill.duration,
            maxSp: this.skill.spData.maxSp,
            initialSp: this.skill.spData.initSp,
            spType: this.skill.spData.spType
        });
    }
    
    // 获取计算器当前设置
    getCalculatorOptions(): string {
        return JSON.stringify({
            potentialRank: this.options.potentialRank || 0,
            moduleLevel: this.options.moduleLevel || 0,
            enemyDef: this.options.enemyDef || 0,
            enemyRes: this.options.enemyRes || 0,
            enemyElementalRes: this.options.enemyElementalRes || 0,
            customBuffs: this.options.customBuffs || {}
        });
    }
}

// 保留默认导出以向后兼容
export default AglinaS3Calculator; 