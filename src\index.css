:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  height: 100vh;
}

.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  display: flex;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.ant-layout-sider {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

/* 自定义增益面板样式 */
.custom-buffs-panel .ant-collapse-header {
  cursor: pointer;
}

.custom-buffs-panel .ant-collapse-content {
  background-color: #f9f9f9;
  transition: all 0.3s;
}

.custom-buffs-panel .ant-form-item-label {
  font-size: 13px;
}

.custom-buffs-panel .ant-input-number {
  width: 100%;
}

/* 技能计算结果样式 */
.skill-result-panel {
  margin-bottom: 24px;
}

.metric-item {
  text-align: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  height: 100%;
}

.metric-label {
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 18px;
  font-weight: bold;
  color: #1890ff;
}

.skill-description {
  white-space: pre-wrap;
  color: #333;
  font-family: inherit;
  background-color: transparent;
  border: none;
  margin: 0;
  padding: 0;
  line-height: 1.6;
}

/* 技能信息项样式 */
.skill-info-item {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 8px 12px;
  height: 100%;
}

.skill-info-label {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

.skill-info-value {
  color: #1890ff;
  font-weight: 500;
} 