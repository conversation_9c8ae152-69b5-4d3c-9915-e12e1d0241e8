import { CharacterData } from '../types/character';
import { EquipmentData } from '../types/equipment';

interface RawCharacterData {
  name: string;
  description: string;
  profession: string;
  subProfessionId: string;
  position: string;
  rarity: number;
  maxLevel: number;
  maxPotentialLevel: number;
  attributes: CharacterData['attributes'];
  talents: CharacterData['talents'];
  skills: CharacterData['skills'];
}

interface RawEquipmentData {
  phases: any[];
}

class DataLoader {
  private static instance: DataLoader;
  private characterData: Record<string, CharacterData> = {};
  private equipmentData: Record<string, EquipmentData[]> = {};
  private skillData: Record<string, any> = {};
  private isLoaded: boolean = false;

  private constructor() {}

  public static getInstance(): DataLoader {
    if (!DataLoader.instance) {
      DataLoader.instance = new DataLoader();
    }
    return DataLoader.instance;
  }

  public async loadData(): Promise<void> {
    if (this.isLoaded) {
      return;
    }

    try {
      const [characterResponse, equipmentResponse, uniequipResponse, skillResponse] = await Promise.all([
        fetch('/data/excel/character_table.json'),
        fetch('/data/excel/battle_equip_table.json'),
        fetch('/data/excel/uniequip_table.json'),
        fetch('/data/excel/skill_table.json')
      ]);

      if (!characterResponse.ok || !equipmentResponse.ok || !uniequipResponse.ok || !skillResponse.ok) {
        throw new Error('Failed to load data files');
      }

      const [characterData, equipmentData, uniequipRawData, skillData] = await Promise.all([
        characterResponse.json(),
        equipmentResponse.json(),
        uniequipResponse.json(),
        skillResponse.json()
      ]);
      
      this.skillData = skillData;
      
      // 提取装备字典数据
      const uniequipData = uniequipRawData.equipDict || {};

      console.log('Raw character data:', characterData);
      console.log('Character data keys:', Object.keys(characterData));
      console.log('Uniequip data keys count:', Object.keys(uniequipData).length);
      console.log('Uniequip data sample:', Object.keys(uniequipData).slice(0, 2).map(key => 
        ({ id: key, name: uniequipData[key].uniEquipName, typeIcon: uniequipData[key].typeIcon })));
      console.log('Skill data keys count:', Object.keys(skillData).length);

      // 先处理角色数据，后处理装备数据
      this.processCharacterData(characterData);
      console.log('Character data processing completed, now processing equipment data');
      this.processEquipmentData(equipmentData, uniequipData);
      
      console.log('Data loading completed, character count:', Object.keys(this.characterData).length);
      console.log('Equipment mapping count:', Object.keys(this.equipmentData).length);
      
      this.isLoaded = true;
    } catch (error) {
      console.error('Error loading data:', error);
      throw error;
    }
  }

  private processCharacterData(data: Record<string, any>): void {
    console.log('Processing character data, input data:', data);
    
    // 检查数据格式
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid character data format: data is not an object');
    }

    console.log('Character data keys count:', Object.keys(data).length);

    // 处理每个角色数据
    for (const [charId, value] of Object.entries(data)) {
      // 只处理 char_ 开头的角色
      if (!charId.startsWith('char_')) {
        console.log(`Skipping non-character data with key: ${charId}`);
        continue;
      }

      console.log(`Processing character ${charId}:`, value);
      console.log(`Character potentialRanks:`, value.potentialRanks);

      // 获取基础属性数据
      const baseAttributes = this.getBaseAttributes(value.phases);

      this.characterData[charId] = {
        charId,
        name: value.name || '未知干员',
        description: value.description || '',
        profession: value.profession || '',
        subProfessionId: value.subProfessionId || '',
        position: value.position || '',
        rarity: value.rarity || 0,
        maxLevel: value.maxLevel || 0,
        maxPotentialLevel: value.potentialRanks?.length || 0,
        attributes: baseAttributes,
        talents: value.talents || [],
        skills: value.skills || [],
        phases: value.phases || [], // 保存phases数据用于后续计算
        potentialRanks: value.potentialRanks || [] // 保存潜能数据
      };
    }

    console.log('Processed character data:', this.characterData);
  }

  // 获取基础属性数据
  private getBaseAttributes(phases: any[]): any {
    if (!phases || !Array.isArray(phases) || phases.length === 0) {
      return {};
    }

    // 获取精英0阶段的属性数据
    const phase0 = phases[0];
    if (!phase0 || !phase0.attributesKeyFrames || !Array.isArray(phase0.attributesKeyFrames)) {
      return {};
    }

    // 获取1级时的属性数据
    const level1Data = phase0.attributesKeyFrames.find(frame => frame.level === 1);
    if (!level1Data || !level1Data.data) {
      return {};
    }

    return {
      maxHp: level1Data.data.maxHp || 0,
      atk: level1Data.data.atk || 0,
      def: level1Data.data.def || 0,
      magicResistance: level1Data.data.magicResistance || 0,
      cost: level1Data.data.cost || 0,
      blockCnt: level1Data.data.blockCnt || 0,
      moveSpeed: level1Data.data.moveSpeed || 0,
      attackSpeed: level1Data.data.attackSpeed || 0,
      baseAttackTime: level1Data.data.baseAttackTime || 0,
      respawnTime: level1Data.data.respawnTime || 0,
      hpRecoveryPerSec: level1Data.data.hpRecoveryPerSec || 0,
      spRecoveryPerSec: level1Data.data.spRecoveryPerSec || 0,
      maxDeployCount: level1Data.data.maxDeployCount || 0,
      maxDeckStackCnt: level1Data.data.maxDeckStackCnt || 0,
      tauntLevel: level1Data.data.tauntLevel || 0,
      massLevel: level1Data.data.massLevel || 0,
      baseForceLevel: level1Data.data.baseForceLevel || 0,
      stunImmune: level1Data.data.stunImmune || false,
      silenceImmune: level1Data.data.silenceImmune || false,
      sleepImmune: level1Data.data.sleepImmune || false,
      frozenImmune: level1Data.data.frozenImmune || false,
      levitateImmune: level1Data.data.levitateImmune || false,
      disarmedCombatImmune: level1Data.data.disarmedCombatImmune || false,
      fearedImmune: level1Data.data.fearedImmune || false
    };
  }

  private processEquipmentData(equipmentData: Record<string, any>, uniequipData: Record<string, any>): void {
    console.log('Processing equipment data');
    
    // 创建角色名称部分到角色ID的映射
    const charNameToIdMap: Record<string, string> = {};
    for (const charId in this.characterData) {
      const nameParts = charId.split('_');
      if (nameParts.length >= 3) {
        // 提取角色名称部分: char_248_mgllan => mgllan
        const charName = nameParts[2];
        charNameToIdMap[charName] = charId;
      }
    }
    
    console.log('Character name to ID mapping:', charNameToIdMap);
    
    // 清空之前的装备数据
    this.equipmentData = {};
    
    for (const [equipId, equipData] of Object.entries(equipmentData)) {
      // 提取charId (格式: uniequip_数字_charId)
      const parts = equipId.split('_');
      if (parts.length < 3) {
        console.warn(`Skipping equipment with invalid ID format: ${equipId}`);
        continue;
      }
      
      // 获取装备中的角色ID部分，必须完全匹配
      const equipCharId = parts.slice(2).join('_');
      
      // 尝试通过名称部分查找角色ID
      let charId = charNameToIdMap[equipCharId];
      
      // 如果找不到映射，则记录警告并跳过
      if (!charId) {
        console.warn(`Cannot map equipment ${equipId} to character ID, skipping`);
        continue;
      }
      
      // 确保该装备确实属于当前角色
      if (equipData.charId && equipData.charId !== charId.replace('char_', '')) {
        console.warn(`Equipment ${equipId} charId mismatch: ${equipData.charId} != ${charId}, skipping`);
        continue;
      }
      
      console.log(`Mapped equipment ${equipId} to character ${charId}`);
      
      // 获取解锁条件 (默认是精英2 60级)
      let unlockCondition = { phase: 2, level: 60 };
      try {
        // 尝试从第一个阶段的第一个part中获取
        if (equipData.phases && 
            equipData.phases[0] && 
            equipData.phases[0].parts && 
            equipData.phases[0].parts[0] && 
            equipData.phases[0].parts[0].overrideTraitDataBundle && 
            equipData.phases[0].parts[0].overrideTraitDataBundle.candidates && 
            equipData.phases[0].parts[0].overrideTraitDataBundle.candidates[0]) {
          
          const candidate = equipData.phases[0].parts[0].overrideTraitDataBundle.candidates[0];
          if (candidate.unlockCondition) {
            // 处理新的解锁条件格式
            const phaseStr = candidate.unlockCondition.phase;
            let phaseNum = 2; // 默认精英2
            
            if (phaseStr === 'PHASE_0') phaseNum = 0;
            else if (phaseStr === 'PHASE_1') phaseNum = 1;
            else if (phaseStr === 'PHASE_2') phaseNum = 2;
            
            unlockCondition = { 
              phase: phaseNum, 
              level: candidate.unlockCondition.level || 60 
            };
          }
        }
      } catch (error) {
        console.warn(`Error extracting unlock condition for ${equipId}:`, error);
      }
      
      // 获取专属装备名称和描述
      let name = equipId;
      let description = '';
      let typeIcon = '';
      
      const equipInfo = uniequipData[equipId];
      if (equipInfo) {
        console.log(`Found equipment info for ${equipId}:`, {
          name: equipInfo.uniEquipName,
          typeIcon: equipInfo.typeIcon
        });
        
        name = equipInfo.uniEquipName || equipId;
        typeIcon = equipInfo.typeIcon || '';
      } else {
        console.warn(`No equipment info found for ${equipId} in uniequip_table`);
        // 从名称中提取简单的装备名
        const nameParts = equipId.split('_');
        const shortName = nameParts[2] || 'unknown';
        name = shortName.charAt(0).toUpperCase() + shortName.slice(1) + " 专属装备";
      }
      
      // 尝试从equipData.phases的parts中提取描述信息
      if (equipData.phases && 
          equipData.phases[0] && 
          equipData.phases[0].parts) {
        
        for (const part of equipData.phases[0].parts) {
          // 从特性数据中提取
          if (part.overrideTraitDataBundle?.candidates?.[0]?.additionalDescription) {
            if (description) description += '\n';
            description += part.overrideTraitDataBundle.candidates[0].additionalDescription;
          }
          
          // 从天赋数据中提取
          if (part.addOrOverrideTalentDataBundle?.candidates?.[0]?.upgradeDescription) {
            if (description) description += '\n';
            description += part.addOrOverrideTalentDataBundle.candidates[0].upgradeDescription;
          }
        }
      }
      
      if (!this.equipmentData[charId]) {
        this.equipmentData[charId] = [];
      }
      
      this.equipmentData[charId].push({
        equipId,
        charId,
        name,
        description,
        typeIcon,
        phases: equipData.phases || [],
        unlockCondition
      });
    }
    
    console.log('Processed equipment data:', this.equipmentData);
  }

  public getCharacter(charId: string): CharacterData | null {
    return this.characterData[charId] || null;
  }

  public getCharacterList(): CharacterData[] {
    return Object.values(this.characterData);
  }

  public getEquipment(charId: string): EquipmentData[] {
    return this.equipmentData[charId] || [];
  }

  public isDataLoaded(): boolean {
    return this.isLoaded;
  }

  // 计算特定等级和精英化阶段的属性
  public calculateAttributes(charId: string, phase: number, level: number): any {
    const character = this.characterData[charId];
    if (!character || !character.phases) {
      return null;
    }

    // 获取指定精英化阶段的数据
    const phaseData = character.phases[phase];
    if (!phaseData || !phaseData.attributesKeyFrames) {
      return null;
    }

    // 获取该阶段的关键帧数据
    const keyFrames = phaseData.attributesKeyFrames;
    if (keyFrames.length < 2) {
      return null;
    }

    // 找到目标等级所在的两个关键帧
    let lowerFrame = keyFrames[0];
    let upperFrame = keyFrames[keyFrames.length - 1];

    for (let i = 0; i < keyFrames.length - 1; i++) {
      if (keyFrames[i].level <= level && keyFrames[i + 1].level > level) {
        lowerFrame = keyFrames[i];
        upperFrame = keyFrames[i + 1];
        break;
      }
    }

    // 线性插值计算属性
    const ratio = (level - lowerFrame.level) / (upperFrame.level - lowerFrame.level);
    const lowerData = lowerFrame.data;
    const upperData = upperFrame.data;

    return {
      maxHp: this.interpolate(lowerData.maxHp, upperData.maxHp, ratio),
      atk: this.interpolate(lowerData.atk, upperData.atk, ratio),
      def: this.interpolate(lowerData.def, upperData.def, ratio),
      magicResistance: this.interpolate(lowerData.magicResistance, upperData.magicResistance, ratio),
      cost: lowerData.cost, // 费用不进行插值
      blockCnt: lowerData.blockCnt, // 阻挡数不进行插值
      moveSpeed: lowerData.moveSpeed, // 移动速度不进行插值
      attackSpeed: lowerData.attackSpeed, // 攻击速度不进行插值
      baseAttackTime: lowerData.baseAttackTime, // 基础攻击间隔不进行插值
      respawnTime: lowerData.respawnTime, // 再部署时间不进行插值
      hpRecoveryPerSec: this.interpolate(lowerData.hpRecoveryPerSec, upperData.hpRecoveryPerSec, ratio),
      spRecoveryPerSec: this.interpolate(lowerData.spRecoveryPerSec, upperData.spRecoveryPerSec, ratio),
      maxDeployCount: lowerData.maxDeployCount, // 最大部署数不进行插值
      maxDeckStackCnt: lowerData.maxDeckStackCnt, // 最大堆叠数不进行插值
      tauntLevel: lowerData.tauntLevel, // 嘲讽等级不进行插值
      massLevel: lowerData.massLevel, // 重量等级不进行插值
      baseForceLevel: lowerData.baseForceLevel, // 基础力量等级不进行插值
      stunImmune: lowerData.stunImmune,
      silenceImmune: lowerData.silenceImmune,
      sleepImmune: lowerData.sleepImmune,
      frozenImmune: lowerData.frozenImmune,
      levitateImmune: lowerData.levitateImmune,
      disarmedCombatImmune: lowerData.disarmedCombatImmune,
      fearedImmune: lowerData.fearedImmune
    };
  }

  // 线性插值计算
  private interpolate(start: number, end: number, ratio: number): number {
    return start + (end - start) * ratio;
  }

  // 判断装备是否已解锁
  public checkEquipmentUnlocked(
    charId: string, 
    phase: number, 
    level: number, 
    equipId: string
  ): boolean {
    const equipments = this.getEquipment(charId);
    const equipment = equipments.find(eq => eq.equipId === equipId);
    
    if (!equipment || !equipment.unlockCondition) {
      return false;
    }
    
    const condition = equipment.unlockCondition;
    return phase >= condition.phase && level >= condition.level;
  }

  // 计算包含装备加成的属性
  public calculateAttributesWithEquipment(
    charId: string, 
    phase: number, 
    level: number,
    equipId?: string,
    equipLevel?: number
  ): any {
    // 计算基础属性
    const baseAttributes = this.calculateAttributes(charId, phase, level);
    
    // 如果没有装备或装备等级，直接返回基础属性
    if (!equipId || !equipLevel || !baseAttributes) {
      console.log('No equipment or base attributes, returning base attributes');
      return baseAttributes;
    }
    
    console.log(`Calculating attributes with equipment: ${equipId}, level: ${equipLevel}`);
    console.log('Base attributes before equipment bonus:', baseAttributes);
    
    // 获取装备
    const equipments = this.getEquipment(charId);
    const equipment = equipments.find(eq => eq.equipId === equipId);
    
    if (!equipment || !equipment.phases) {
      console.warn(`Equipment ${equipId} not found or has no phases`);
      return baseAttributes;
    }
    
    // 根据equipLevel查找对应的装备阶段
    const equipPhase = equipment.phases.find(p => p.equipLevel === equipLevel);
    
    if (!equipPhase) {
      console.warn(`Equipment level ${equipLevel} not found in equipment ${equipId}`);
      console.log('Available equipment levels:', equipment.phases.map(p => p.equipLevel));
      return baseAttributes;
    }
    
    if (!equipPhase.attributeBlackboard) {
      console.warn(`Equipment phase has no attributeBlackboard for ${equipId} level ${equipLevel}`);
      return baseAttributes;
    }
    
    console.log(`Found equipment phase with equipLevel=${equipPhase.equipLevel}`);
    console.log(`Equipment attributeBlackboard:`, equipPhase.attributeBlackboard);
    
    // 创建一个新对象作为结果
    const result = JSON.parse(JSON.stringify(baseAttributes));
    
    // 确保 originalValues 存在
    result.originalValues = {};
    
    // 记录所有属性的原始值，以便正确显示
    for (const key in result) {
      if (typeof result[key] === 'number' && key !== 'originalValues') {
        result.originalValues[key] = result[key];
      }
    }
    
    // 应用装备加成
    for (const bonus of equipPhase.attributeBlackboard) {
      const key = this.mapEquipAttributeKey(bonus.key);
      console.log(`Processing bonus: ${bonus.key} -> mapped to -> ${key}, value: ${bonus.value}`);
      
      if (key && result[key] !== undefined) {
        // 加上装备加成
        const originalValue = result[key];
        result[key] += bonus.value;
        console.log(`Applied bonus to ${key}: +${bonus.value} (${originalValue} -> ${result[key]})`);
      } else {
        console.log(`Skipped bonus ${bonus.key}: mapped key=${key}, exists in attributes=${key ? (result[key] !== undefined) : false}`);
      }
    }
    
    console.log('Final attributes after equipment bonus:', result);
    return result;
  }

  // 映射装备属性键到角色属性键
  private mapEquipAttributeKey(equipKey: string): string | null {
    const keyMap: Record<string, string> = {
      'max_hp': 'maxHp',
      'atk': 'atk',
      'def': 'def',
      'attack_speed': 'attackSpeed',
      'magic_resistance': 'magicResistance',
      'cost': 'cost',
      'block_cnt': 'blockCnt',
      'respawn_time': 'respawnTime'
    };
    
    // 尝试直接映射
    if (keyMap[equipKey]) {
      return keyMap[equipKey];
    }
    
    // 记录未识别的属性键以便后续扩展映射表
    console.warn(`Unrecognized equipment attribute key: ${equipKey}`);
    return null;
  }

  // 获取技能详细信息
  public getSkillInfo(skillId: string): any {
    if (!this.skillData || !skillId) {
      return null;
    }
    
    return this.skillData[skillId] || null;
  }
}

export default DataLoader; 