# Momo's Arknights Toolkit 工作记录

## 已完成工作

### 1. 项目初始化
- 使用 Vite + React + TypeScript 创建项目
- 安装核心依赖：antd、@ant-design/icons、react-router-dom
- 创建基础项目结构：
  - src/layouts：布局组件
  - src/pages：页面组件
  - src/components：通用组件
  - src/hooks：自定义 hooks
  - src/utils：工具函数

### 2. 布局实现
- 主布局组件 (MainLayout.tsx)
  - 顶部标题栏：显示 "Momo's Arknights Toolkit"
  - 左侧导航菜单：包含首页、DPS计算器、敌人数据库入口
  - 内容区域：使用 Outlet 展示具体页面内容
  - 使用 Ant Design 的 Layout 组件实现响应式布局

### 3. 页面组件
- 首页 (Home.tsx)
  - 展示工具集简介
  - 列出主要功能模块
- DPS计算器页面 (DpsCalculator.tsx)
  - 实现数据加载和错误处理
  - 添加干员选择功能
  - 展示干员详细信息
  - 添加专属装备选择功能
  - 添加技能选择功能
  - 使用 Ant Design 组件实现美观的 UI
- 敌人数据库页面 (EnemyDatabase.tsx)
  - 预留功能开发接口
  - 显示开发中提示

### 4. 路由配置
- 使用 react-router-dom 配置路由
- 实现嵌套路由结构
- 路由路径：
  - /：首页
  - /dps-calculator：DPS计算器
  - /enemy-database：敌人数据库

### 5. 样式配置
- 全局样式设置 (index.css)
  - 基础字体和布局设置
  - Ant Design 组件样式优化
  - 响应式布局支持

### 6. Docker 支持
- 创建 Dockerfile
  - 使用 node:18-alpine 作为基础镜像
  - 配置开发环境
  - 暴露 5173 端口
- 创建 .dockerignore
  - 排除不必要的文件
  - 优化构建过程

### 7. 数据预处理
- 创建数据模型
  - 角色数据模型 (src/types/character.ts)
    - CharacterSkill：技能数据接口
    - CharacterTalent：天赋数据接口
    - CharacterData：角色基础数据接口
  - 专属装备数据模型 (src/types/equipment.ts)
    - EquipmentAttribute：装备属性接口
    - EquipmentData：装备基础数据接口
- 实现数据加载工具类 (src/utils/dataLoader.ts)
  - 单例模式实现
  - 异步加载数据文件
  - 数据处理和转换
  - 提供数据访问接口
    - getCharacter：获取单个角色数据
    - getCharacterList：获取角色列表
    - getEquipment：获取角色专属装备
    - isDataLoaded：检查数据加载状态
  - 实现属性计算功能
    - getBaseAttributes：获取基础属性数据
    - calculateAttributes：计算特定等级和精英化阶段的属性
    - interpolate：实现属性值的线性插值计算
  - 数据过滤优化
    - 只处理 char_ 开头的角色数据
    - 跳过非角色数据的处理

### 8. DPS计算器功能增强
- 实现干员属性动态计算
  - 支持精英化阶段切换（精英0/1/2）
  - 支持等级调整（1-90级）
  - 实现属性值的实时计算和更新
- 优化属性显示
  - 数值属性四舍五入显示
  - 添加百分比和单位显示
  - 展示更多属性信息（攻击间隔、再部署时间等）
- 完善干员信息展示
  - 添加精英化阶段选择
  - 添加等级选择
  - 显示详细属性面板

### 9. 干员信息展示优化
- 改进精英化阶段选择
  - 根据干员的phases数组动态确定可用的精英化阶段选项
  - 确保一二三星干员只显示实际可用的精英化阶段
  - 自动处理不同星级干员的精英化限制
- 精确的等级范围控制
  - 从attributesKeyFrames关键帧数据中获取精确的最小和最大等级
  - 根据当前精英化阶段动态调整等级选择范围
  - 切换精英化阶段时自动设置为该阶段的最小等级
- 用户界面优化
  - 添加子职业显示
  - 移除重复的精英化阶段和等级显示
  - 更合理布局干员信息面板
- 数据过滤完善
  - 确保只处理以char_开头的正式角色数据
  - 优化数据加载和处理流程

### 10. 专属装备功能实现
- 新增装备数据模型与数据加载
  - 创建EquipmentPhase、EquipmentAttribute等接口定义
  - 处理装备属性加成与描述信息
  - 兼容JSON数据结构变化
- 装备选择功能
  - 根据角色ID和等级条件动态显示可用装备
  - 展示装备名称和类型图标
  - 添加装备等级选择（一级/二级/三级模组）
- 装备效果展示
  - 展示装备属性加成效果（攻击力、生命值等）
  - 以"总数值(+加成值)"格式清晰显示加成效果
  - 显示装备特性和天赋变更信息
- 属性计算优化
  - 集成装备属性加成到角色属性计算中
  - 正确处理不同阶段装备的属性变化
  - 处理装备解锁条件检查
- 用户界面优化
  - 添加类型图标显示
  - 优化装备描述展示
  - 提供直观的装备选择体验

### 11. 数据源适配改进
- 适配新的角色数据结构
  - 更新角色数据处理逻辑，支持对象格式
  - 优化角色属性加载和计算
- 适配装备数据结构
  - 处理从uniequip_table.json中提取名称和图标
  - 从battle_equip_table.json中提取装备效果描述
  - 正确解析装备解锁条件格式
- 数据加载流程优化
  - 添加详细日志记录功能
  - 提高数据处理的健壮性
  - 优化错误处理和异常情况处理

### 12. 技能计算系统开发
- 抽象基类设计
  - 创建`SkillCalculator`抽象基类作为所有计算器的模板
  - 定义各种输出指标接口(DPS、平均DPS、单次伤害等)
  - 实现访问控制逻辑，只显示有效的输出指标
- 数据结构设计
  - 定义`SkillOptions`接口用于配置计算参数
  - 实现各种计算器需要的辅助方法
- 工厂模式实现
  - 创建`SkillCalculatorFactory`用于生成具体的计算器实例
  - 实现动态加载机制，根据干员和技能ID自动匹配计算器
    - 命名约定：`{干员名}S{技能序号}Calculator.ts`，如`AngelinaS3Calculator.ts`
    - 文件位置：所有技能计算器统一放在`src/utils/skillCalculators/`目录
    - 命名格式化：干员名称首字母大写，如`char_291_aglina`转换为`Aglina`
    - 正则匹配：使用`character.charId.match(/char_(\d+)_(\w+)/)`提取干员信息
    - 类名与文件名保持一致，确保可以通过动态导入获取
    - 查找路径：通过正则提取干员编号和名称，构造文件路径
    - 多层加载机制：先查找缓存、再查预加载列表、再动态导入、最后使用默认计算器
    - 支持异步加载：使用`import()`进行代码分割，避免一次性加载所有计算器
    - 错误处理：导入失败时自动降级到默认计算器，并记录日志
  - 设计多层加载策略：缓存、预加载、动态导入
- 示例计算器实现
  - 开发`DefaultSkillCalculator`作为通用计算器
  - 实现`AngelinaS3Calculator`作为特殊技能示例
  - 添加基础数值计算逻辑
- 配套UI组件
  - 创建`SkillResultPanel`组件用于展示计算结果
  - 实现指标动态显示逻辑
  - 添加技能描述展示功能

### 13. 潜能系统实现
- 潜能选择功能
  - 将潜能选择移到角色信息区域，与精英化和等级选择同级
  - 实现潜能等级选择UI，显示每个潜能的具体效果
  - 根据游戏数据动态生成潜能选项
  - 当没有详细潜能数据时仍提供基本选择
- 潜能数据处理工具
  - 创建`potentialHelper.ts`专门处理潜能相关数据和计算
  - 实现`formatPotentialEffect`函数用于格式化潜能描述
  - 实现`applyPotentialBonus`函数计算潜能加成属性
  - 支持不同公式类型(ADDITION和MULTIPLY)的属性修改
- 潜能属性加成显示
  - 使用蓝色文字显示潜能带来的属性加成
  - 区分装备加成(黑色)和潜能加成(蓝色)
  - 当属性有多种加成时正确显示
- 潜能与装备协同
  - 解决选择模组后潜能加成被清除的问题
  - 创建统一的`updateAttributesWithEquipmentAndPotential`函数
  - 确保在切换装备和装备等级时保留潜能加成

### 14. 技能计算器改进
- TypeScript类型修复
  - 修复`AngelinaS3Calculator`类中的访问修饰符问题
  - 将`calculateDamage`和`calculateFrameAlignment`方法从`private`改为`protected`
  - 解决继承结构中的访问修饰符冲突
- 技能选择增强
  - 从`skill_table.json`获取技能详细信息
  - 添加技能等级选择功能
  - 实现技能描述的参数替换和格式化显示
- 技能计算系统优化
  - 实现多种伤害类型的计算(物理、法术、元素、真实伤害)
  - 支持技能持续时间和冷却时间的动态计算
  - 计算平均DPS时考虑技能循环和普通攻击

### 15. 界面与显示优化
- 整体UI交互改进
  - 修复精英化等级切换错误问题
  - 优化角色选择逻辑，默认选择最高精英阶段和最高等级
  - 添加更多调试日志，便于排查问题
- 专属装备描述格式化与处理
  - 增加富文本标记(`<@ba.xxx>`、`<$ba.xxx>`)清理功能
  - 处理变量替换，支持从`blackboard`数据中替换`{key:default}`格式的变量
  - 正确转换百分比显示，如`0.6`转换为`60%`
  - 根据`requiredPotentialRank`筛选显示合适的潜能描述
- 技能描述优化
  - 实现技能描述变量替换与富文本标记清理
  - 支持处理`duration`等常规参数与`blackboard`中的特殊参数
  - 统一技能与装备的描述处理逻辑
- 属性加成显示优化
  - 将潜能加成显示为黑色，与基础属性风格保持一致
  - 将装备加成显示为蓝色，区分不同来源的加成
  - 调整加成显示顺序，将潜能加成显示在装备加成前面
  - 为装备信息栏中的属性加成添加蓝色样式

### 16. 代码结构优化
- 通用组件抽象
  - 创建`processEquipmentDescription`函数处理装备描述变量替换
  - 创建`processSkillDescription`函数处理技能描述变量替换
  - 增强`cleanRichTextTags`函数处理各种富文本标记
- 数据处理逻辑优化
  - 添加`getCharacterMaxLevel`辅助函数，优化等级选择
  - 改进`handleCharacterChange`和`handlePhaseChange`以支持更好的默认值选择
  - 创建更健壮的数据验证和错误处理机制
- 工具函数增强
  - 改进`formatAttributeWithBonus`函数，支持显示多种来源的加成
  - 增强加成显示逻辑，支持负数加成的正确显示

## 待开发功能

### 1. DPS计算器
- 干员数据输入界面
  - ~~添加等级选择~~ ✓
  - ~~添加精英化阶段选择~~ ✓
  - ~~添加专属装备选择~~ ✓
  - ~~添加潜能选择~~ ✓
  - ~~添加技能等级选择~~ ✓
- 伤害计算公式实现
  - 基础伤害计算
  - 技能倍率计算
  - 天赋加成计算
  - 专属装备加成计算
- 结果展示和导出功能
  - 添加伤害数据展示
  - 添加图表展示
  - 添加数据导出功能

### 2. 敌人数据库
- 敌人数据展示界面
- 搜索和筛选功能
- 详细数据展示

### 3. 前端存储
- 实现临时数据存储
- 考虑使用 localStorage 或 IndexedDB
- 为后续后端迁移预留接口

## 技术栈
- 前端框架：React + TypeScript
- UI 组件库：Ant Design
- 路由管理：React Router
- 构建工具：Vite
- 容器化：Docker

## 开发环境
- 开发服务器：Vite Dev Server
- 端口：5173
- 支持热更新 