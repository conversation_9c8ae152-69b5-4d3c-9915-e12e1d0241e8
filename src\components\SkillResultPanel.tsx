import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Divider, Tag, Button } from 'antd';
import { SkillCalculator, SkillOptions, CustomBuffs } from '../types/skill';
import { CharacterData } from '../types/character';
import { CharacterSkill } from '../types/character';
import SkillCalculatorFactory from '../utils/SkillCalculatorFactory';
import { DefaultSkillCalculator } from '../utils/skillCalculators/DefaultSkillCalculator';
import DataLoader from '../utils/dataLoader';

interface SkillResultPanelProps {
  character: CharacterData;
  skill: CharacterSkill;
  skillLevel: number;
  options: SkillOptions;
  onOptionsChange?: (options: SkillOptions) => void;
}

const SkillResultPanel: React.FC<SkillResultPanelProps> = ({
  character,
  skill,
  skillLevel,
  options,
  onOptionsChange,
}) => {
  const [calculator, setCalculator] = useState<SkillCalculator>(
    new DefaultSkillCalculator(character, skill, skillLevel, options)
  );
  const [skillInfo, setSkillInfo] = useState<any>(null);
  const [showDebugInfo, setShowDebugInfo] = useState<boolean>(false);

  useEffect(() => {
    // 异步加载正确的计算器
    SkillCalculatorFactory.createCalculatorAsync(character, skill, skillLevel, options)
      .then(calculatorInstance => {
        setCalculator(calculatorInstance);
      })
      .catch(error => {
        console.error('加载计算器失败:', error);
      });

    // 获取技能详细信息
    const dataLoader = DataLoader.getInstance();
    const skillDetails = dataLoader.getSkillInfo(skill.skillId);
    setSkillInfo(skillDetails);

    // 检查URL是否有debug参数
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('debug')) {
      setShowDebugInfo(true);
    }
  }, [character, skill, skillLevel, options]);

  // 获取所有有效指标
  const availableMetrics = calculator.getAvailableMetrics();

  // 指标显示名称映射
  const metricNames: Record<string, string> = {
    dps: "DPS",
    averageDps: "平均DPS",
    totalDamage: "总伤害",
    dph: "单次伤害",
    eps: "每秒元素伤害",
    averageEps: "平均每秒元素伤害",
    totalElementalDamage: "总元素伤害",
    hps: "每秒治疗",
    totalHealing: "总治疗量",
    averageHps: "平均每秒治疗"
  };

  // 获取指标值的方法映射
  const metricGetters: Record<string, () => string> = {
    dps: calculator.getDps.bind(calculator),
    averageDps: calculator.getAverageDps.bind(calculator),
    totalDamage: calculator.getTotalDamage.bind(calculator),
    dph: calculator.getDph.bind(calculator),
    eps: calculator.getEps.bind(calculator),
    averageEps: calculator.getAverageEps.bind(calculator),
    totalElementalDamage: calculator.getTotalElementalDamage.bind(calculator),
    hps: calculator.getHps.bind(calculator),
    totalHealing: calculator.getTotalHealing.bind(calculator),
    averageHps: calculator.getAverageHps.bind(calculator)
  };

  // 获取当前技能详情
  const getCurrentLevelSkillInfo = () => {
    if (!skillInfo || !skillInfo.levels || skillInfo.levels.length === 0) {
      return null;
    }

    // 技能等级从0开始，但显示从1开始，因此减1
    return skillInfo.levels[skillLevel - 1];
  };

  // 处理技能描述，替换转义字符和变量
  const formatSkillDescription = (description: string) => {
    if (!description) return "";

    // 如果有计算器，使用计算器的公共处理方法
    if (calculator && currentLevelInfo) {
      return calculator.processDescription(description, currentLevelInfo.blackboard);
    }

    // 替换转义的换行符
    let formatted = description.replace(/\\n/g, '\n');

    // 替换特殊标签，简化处理
    formatted = formatted.replace(/<@ba\.vup>\{(.+?)\}<\/>/g, '$1');
    formatted = formatted.replace(/<@ba\.rem>(.+?)<\/>/g, '$1');
    formatted = formatted.replace(/<@ba\.kw>(.+?)<\/>/g, '$1');
    formatted = formatted.replace(/<.*?>/g, ''); // 删除其他标签

    return formatted;
  };

  // 如果没有可用指标，显示提示信息
  if (availableMetrics.length === 0) {
    return (
      <Card title="技能计算结果" className="skill-result-panel">
        <p>此技能没有可计算的输出指标</p>
      </Card>
    );
  }

  // 当前技能等级的详细信息
  const currentLevelInfo = getCurrentLevelSkillInfo();
  const spData = currentLevelInfo?.spData || skill.spData;
  const skillDuration = currentLevelInfo?.duration !== undefined ?
    currentLevelInfo.duration : skill.duration;

  return (
    <Card title="技能计算结果" className="skill-result-panel">
      {/* 计算指标 */}
      <Card type="inner" title="伤害/治疗计算" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          {availableMetrics.map((metric) => (
            <Col span={8} key={metric}>
              <div className="metric-item">
                <div className="metric-label">{metricNames[metric]}</div>
                <div className="metric-value">{metricGetters[metric]()}</div>
              </div>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 额外显示原始技能名称，方便调试 */}
      {showDebugInfo && currentLevelInfo && (
        <>
          <Card type="inner" title="调试信息" style={{ marginTop: 16 }}>
            <p>技能名称: {currentLevelInfo.name}</p>
            <p>持续类型: {currentLevelInfo.durationType}</p>
            <p>SP类型: {currentLevelInfo.spData?.spType}</p>
            <p>技能类型: {currentLevelInfo.skillType}</p>
            {currentLevelInfo.blackboard && (
              <>
                <p>黑板参数:</p>
                <ul>
                  {currentLevelInfo.blackboard.map((item: any, index: number) => (
                    <li key={index}>{item.key}: {item.value}</li>
                  ))}
                </ul>
              </>
            )}
            {currentLevelInfo.description && (
              <p>原始技能描述: {currentLevelInfo.description}</p>
            )}
            {options.customBuffs && Object.keys(options.customBuffs).length > 0 && (
              <>
                <p>自定义增益:</p>
                <ul>
                  {Object.entries(options.customBuffs).map(([key, value]) => (
                    <li key={key}>{key}: {value}</li>
                  ))}
                </ul>
              </>
            )}
          </Card>
        </>
      )}
      {!showDebugInfo && (
        <div style={{ textAlign: 'center', marginTop: 8 }}>
          <Button type="link" onClick={() => setShowDebugInfo(true)}>
            显示调试信息
          </Button>
        </div>
      )}
    </Card>
  );
};

export default SkillResultPanel;