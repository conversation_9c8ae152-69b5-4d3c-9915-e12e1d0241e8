import React, { useState } from 'react';
import { Card, Collapse, Form, InputNumber, Row, Col, Typography, Tooltip, Button } from 'antd';
import { CustomBuffs } from '../types/skill';
import { InfoCircleOutlined, PlusOutlined, MinusOutlined } from '@ant-design/icons';

const { Panel } = Collapse;
const { Title, Text } = Typography;

interface CustomBuffsPanelProps {
  value?: CustomBuffs;
  onChange?: (value: CustomBuffs) => void;
}

const CustomBuffsPanel: React.FC<CustomBuffsPanelProps> = ({ value = {}, onChange }) => {
  const [expanded, setExpanded] = useState(false);
  
  // 处理任意字段的变更
  const handleChange = (field: keyof CustomBuffs, newValue: number | undefined) => {
    if (onChange) {
      const newBuffs = { ...value, [field]: newValue };
      onChange(newBuffs);
    }
  };
  
  // 重置所有增益
  const resetBuffs = () => {
    if (onChange) {
      onChange({});
    }
  };
  
  return (
    <Card 
      className="custom-buffs-panel"
      title={
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>自定义增益</span>
          <Button 
            type="text" 
            icon={expanded ? <MinusOutlined /> : <PlusOutlined />} 
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? '收起' : '展开'}
          </Button>
        </div>
      }
      style={{ marginBottom: 16 }}
    >
      <Collapse activeKey={expanded ? ['1'] : []} ghost>
        <Panel key="1" header={null} style={{ padding: 0 }}>
          <Form layout="vertical">
            {/* 攻击力相关增益 */}
            <Title level={5}>攻击力增益</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="加法作用于攻击力，如「乌尔比安」二天赋">
                      攻击力直接加算 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.attackFirstValue}
                    onChange={(val) => handleChange('attackFirstValue', val as number)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="百分比作用于攻击力，如「Mon3tr」一天赋">
                      攻击力直接乘算 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.attackFirstRatio}
                    onChange={(val) => handleChange('attackFirstRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="最终加算至攻击力，如「斯卡蒂」二技能">
                      攻击力最终加算 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.attackFinalValue}
                    onChange={(val) => handleChange('attackFinalValue', val as number)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 攻击速度相关增益 */}
            <Title level={5}>攻击速度增益</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="攻击速度提升百分比，如「安洁莉娜」一技能">
                      攻击速度提升 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.attackSpeedValue}
                    onChange={(val) => handleChange('attackSpeedValue', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 敌人属性相关增益 */}
            <Title level={5}>敌人属性降低</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="敌人防御力减少，如「陈」三技能">
                      敌人防御力减少 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyDefLoss}
                    onChange={(val) => handleChange('enemyDefLoss', val as number)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="敌人法术抗性减少，如「德克萨斯」二技能">
                      敌人法抗减少 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyResLoss}
                    onChange={(val) => handleChange('enemyResLoss', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 伤害增幅相关增益 */}
            <Title level={5}>伤害增幅</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="所有伤害类型增幅">
                      伤害增幅 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.damageAmplifyRatio}
                    onChange={(val) => handleChange('damageAmplifyRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="物理伤害增幅">
                      物理伤害增幅 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.physicalDamageAmplifyRatio}
                    onChange={(val) => handleChange('physicalDamageAmplifyRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="法术伤害增幅，如「塞雷娅」三技能">
                      法术伤害增幅 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.magicalDamageAmplifyRatio}
                    onChange={(val) => handleChange('magicalDamageAmplifyRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="元素伤害增幅">
                      元素伤害增幅 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.elementalDamageAmplifyRatio}
                    onChange={(val) => handleChange('elementalDamageAmplifyRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 特殊增益 */}
            <Title level={5}>特殊增益</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，如「赫拉格」二技能">
                      精力充沛 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.energizedAttackRatio}
                    onChange={(val) => handleChange('energizedAttackRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，如「斯卡蒂」二技能">
                      鼓舞 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.encouragedAttackValue}
                    onChange={(val) => handleChange('encouragedAttackValue', val as number)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 敌人状态 */}
            <Title level={5}>敌人状态</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，所有伤害类型增幅">
                      脆弱 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyVulnerableRatio}
                    onChange={(val) => handleChange('enemyVulnerableRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，物理伤害增幅">
                      物理脆弱 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyVulnerablePhysicalRatio}
                    onChange={(val) => handleChange('enemyVulnerablePhysicalRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，法术伤害增幅">
                      法术脆弱 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyVulnerableMagicalRatio}
                    onChange={(val) => handleChange('enemyVulnerableMagicalRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="同类属性取最高，元素伤害增幅">
                      元素脆弱 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.enemyVulnerableElementalRatio}
                    onChange={(val) => handleChange('enemyVulnerableElementalRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 技力相关 */}
            <Title level={5}>技力相关</Title>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item 
                  label={
                    <Tooltip title="技力回复速度提升，同类属性取最高">
                      技力回复速度 <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <InputNumber
                    value={value.skillPointRecoveryRatio}
                    onChange={(val) => handleChange('skillPointRecoveryRatio', val as number)}
                    style={{ width: '100%' }}
                    formatter={(value) => `${value}%`}
                    parser={(value) => parseFloat(value!.replace('%', ''))}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* 重置按钮 */}
            <Row justify="end">
              <Col>
                <Button onClick={resetBuffs}>重置所有增益</Button>
              </Col>
            </Row>
          </Form>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default CustomBuffsPanel; 