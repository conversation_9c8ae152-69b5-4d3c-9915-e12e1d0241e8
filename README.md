# 明日方舟伤害计算器 (Ark DPS Calculator)

这是一个用于计算明日方舟游戏中技能DPS的Web应用工具。该工具可以帮助玩家创建、管理和计算干员技能的伤害效果。

## 功能特性

- **攻击模式管理**
  - 创建自定义攻击模式
  - 支持多种伤害类型（物理、魔法、真实、混合）
  - 可设置伤害倍率、触发几率、持续时间等参数
  - 支持添加附加效果（减速、眩晕、流血等）

- **技能编辑器**
  - 组合多个攻击模式创建技能
  - 自定义技能名称和描述
  - 支持技能数据的本地存储
  - 可导出技能数据为JSON格式

- **数据管理**
  - 使用 IndexedDB 进行本地数据存储
  - 支持数据的导入导出
  - 数据持久化，刷新页面不丢失

## 技术栈

- React.js
- Ant Design 组件库
- IndexedDB 数据库
- React Router 路由管理

## 安装说明

1. 克隆项目到本地：
```bash
git clone [项目地址]
cd ark_dps
```

2. 安装依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm start
```

4. 在浏览器中访问：
```
http://localhost:3000
```

## 使用说明

### 攻击模式创建

1. 点击左侧菜单的"攻击模式"
2. 填写攻击模式的基本信息：
   - 效果名称
   - 效果类型（直接伤害、持续伤害、范围伤害等）
   - 伤害类型（物理、魔法、真实、混合）
3. 设置效果参数：
   - 基础伤害值/倍率
   - 作用次数
   - 效果间隔
   - 持续时间
   - 触发几率
   - 影响范围
4. 添加附加效果（可选）
5. 点击"保存效果"按钮

### 技能创建

1. 点击左侧菜单的"技能"
2. 填写技能信息：
   - 技能名称
   - 选择攻击模式（可多选）
   - 添加技能描述
3. 点击"保存技能"按钮

### 数据管理

- 查看已保存的技能：点击"查看已保存的技能"按钮
- 导出数据：点击"导出JSON"按钮
- 删除技能：在技能列表中点击对应技能的"删除"按钮

## 开发计划

- [ ] 添加技能DPS计算功能
- [ ] 支持技能数据导入
- [ ] 添加更多自定义选项
- [ ] 优化用户界面和交互体验

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。

## 许可证

MIT License
