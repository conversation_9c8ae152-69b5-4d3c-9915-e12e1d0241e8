import { SkillCalculator } from "../../types/skill";
import { DamageType } from "../SkillCalculator";
import { CharacterData, CharacterSkill } from "../../types/character";
import { SkillOptions } from "../../types/skill";

/**
 * 示例计算器，展示如何使用自定义增益
 */
export class ExampleCalculator extends SkillCalculator {
  constructor(
    character: CharacterData,
    skill: CharacterSkill,
    skillLevel: number,
    options: SkillOptions
  ) {
    super(character, skill, skillLevel, options);
  }
  
  // 获取伤害类型
  protected getDamageType(): DamageType {
    return DamageType.PHYSICAL;
  }
  
  // 获取自定义增益
  protected getCustomBuffs() {
    return this.options.customBuffs || {};
  }
  
  // 应用自定义增益到攻击力
  protected applyCustomBuffsToAttack(baseAttack: number): number {
    const buffs = this.getCustomBuffs();
    let result = baseAttack;
    
    // 攻击力直接加算
    if (buffs.attackFirstValue) {
      result += buffs.attackFirstValue;
    }
    
    // 攻击力直接乘算 (百分比)
    if (buffs.attackFirstRatio) {
      result *= (1 + buffs.attackFirstRatio / 100);
    }
    
    // 攻击力最终加算
    if (buffs.attackFinalValue) {
      result += buffs.attackFinalValue;
    }
    
    return result;
  }
  
  // 应用自定义增益到敌人防御
  protected applyCustomBuffsToEnemyDef(enemyDef: number): number {
    const buffs = this.getCustomBuffs();
    let result = enemyDef;
    
    // 敌人防御力减少
    if (buffs.enemyDefLoss) {
      result = Math.max(0, result - buffs.enemyDefLoss);
    }
    
    return result;
  }
  
  // 应用自定义增益到伤害
  protected applyCustomBuffsToDamage(damage: number): number {
    const buffs = this.getCustomBuffs();
    let result = damage;
    
    // 通用伤害增幅
    if (buffs.damageAmplifyRatio) {
      result *= (1 + buffs.damageAmplifyRatio / 100);
    }
    
    // 物理伤害增幅
    if (buffs.physicalDamageAmplifyRatio) {
      result *= (1 + buffs.physicalDamageAmplifyRatio / 100);
    }
    
    // 敌人脆弱状态
    if (buffs.enemyVulnerableRatio) {
      result *= (1 + buffs.enemyVulnerableRatio / 100);
    }
    
    // 敌人物理脆弱
    if (buffs.enemyVulnerablePhysicalRatio) {
      result *= (1 + buffs.enemyVulnerablePhysicalRatio / 100);
    }
    
    return result;
  }
  
  // 获取DPS输出
  getDps(): string {
    // 基础攻击力
    let baseAtk = this.character.attributes.atk || 500; // 默认值
    
    // 从技能的blackboard中查找攻击力加成
    const atkMultiplier = this.skill.blackboard.find(item => item.key === "atk")?.value || 0;
    
    // 应用技能加成
    let skillBoostAtk = baseAtk * (1 + atkMultiplier);
    
    // 应用自定义增益
    skillBoostAtk = this.applyCustomBuffsToAttack(skillBoostAtk);
    
    // 敌人防御力
    const enemyDef = this.options.enemyDef || 0;
    const actualDef = this.applyCustomBuffsToEnemyDef(enemyDef);
    
    // 计算基础伤害
    let damage = Math.max(skillBoostAtk - actualDef, skillBoostAtk * 0.05);
    
    // 应用自定义增益到伤害
    damage = this.applyCustomBuffsToDamage(damage);
    
    // 计算DPS (假设攻击间隔是1秒)
    const attackInterval = this.character.attributes.baseAttackTime || 1;
    const dps = damage / attackInterval;
    
    // 返回格式化的DPS值
    return dps.toFixed(2);
  }
  
  // 获取平均DPS（考虑技能持续时间与冷却时间）
  getAverageDps(): string {
    // 获取技能持续时间
    const duration = this.skill.duration || 15; // 默认15秒
    
    // 获取技能SP
    const spData = this.skill.spData || { spType: 1, maxSp: 20, initSp: 0 };
    
    // 计算技能循环时间（持续时间 + 冷却时间）
    const cooldown = spData.maxSp;
    const totalCycle = duration + cooldown;
    
    // 计算单次技能期间的总伤害
    const dps = parseFloat(this.getDps());
    const skillDamage = dps * duration;
    
    // 计算平均DPS
    const averageDps = skillDamage / totalCycle;
    
    return averageDps.toFixed(2);
  }
  
  // 获取技能总伤害
  getTotalDamage(): string {
    // 基础DPS
    const dps = parseFloat(this.getDps());
    
    // 技能持续时间
    const duration = this.skill.duration || 15; // 默认15秒
    
    // 计算总伤害
    const totalDamage = dps * duration;
    
    return Math.round(totalDamage).toString();
  }
}

// 保留默认导出
export default ExampleCalculator;