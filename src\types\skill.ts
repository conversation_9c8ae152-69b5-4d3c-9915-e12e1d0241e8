// 技能计算器选项接口
export interface SkillOptions {
  potentialRank?: number;
  moduleLevel?: number;
  enemyDef?: number;
  enemyRes?: number;
  enemyElementalRes?: number; // 敌人元素抗性
  customBuffs?: CustomBuffs; // 添加自定义增益

  // 手动输入的乘区加成
  directAtkAddition?: number;       // A区: 攻击力直接加算
  directAtkMultiplier?: number;     // B区: 攻击力直接乘算 (百分比，如0.15表示+15%)
  finalAtkAddition?: number;        // C区: 攻击力最终加算
  skillMultiplierBonus?: number;    // D区: 技能倍率额外加成 (百分比，如0.2表示+20%)
  damageAddition?: number;          // E区: 伤害加算
  finalDamageMultiplier?: number;   // F区: 伤害最终乘算 (百分比，如0.3表示+30%)

  // 敌人双抗减免
  enemyDefReduction?: number;       // 敌人防御力减免
  enemyResReduction?: number;       // 敌人法抗减免 (百分比，如0.2表示-20%)
}

// 自定义增益接口
export interface CustomBuffs {
  // 攻击力相关
  attackFirstValue?: number; // 攻击力直接加算
  attackFirstRatio?: number; // 攻击力直接乘算
  attackFinalValue?: number; // 攻击力最终加算
  
  // 攻击速度相关
  attackSpeedValue?: number; // 攻击速度提升
  
  // 敌人属性相关
  enemyDefLoss?: number; // 敌人防御力降低
  enemyResLoss?: number; // 敌人法抗降低
  
  // 伤害增幅相关
  damageAmplifyRatio?: number; // 伤害增幅
  physicalDamageAmplifyRatio?: number; // 物理伤害增幅
  magicalDamageAmplifyRatio?: number; // 法术伤害增幅
  elementalDamageAmplifyRatio?: number; // 元素伤害增幅
  
  // 特殊增益
  energizedAttackRatio?: number; // 精力充沛
  encouragedAttackValue?: number; // 鼓舞
  
  // 敌人状态
  enemyVulnerableRatio?: number; // 脆弱
  enemyVulnerablePhysicalRatio?: number; // 物理脆弱
  enemyVulnerableMagicalRatio?: number; // 法术脆弱
  enemyVulnerableElementalRatio?: number; // 元素脆弱
  
  // 技力相关
  skillPointRecoveryRatio?: number; // 技力回复速度提升
}

// 抽象技能计算器基类
export abstract class SkillCalculator {
  constructor(
    protected character: any,
    protected skill: any,
    protected skillLevel: number,
    protected options: SkillOptions
  ) {}

  // 基础输出方法
  getDps(): string { return ""; }
  getAverageDps(): string { return ""; }
  getTotalDamage(): string { return ""; }
  getDph(): string { return ""; }
  getEps(): string { return ""; }
  getAverageEps(): string { return ""; }
  getTotalElementalDamage(): string { return ""; }
  getHps(): string { return ""; }
  getTotalHealing(): string { return ""; }
  getAverageHps(): string { return ""; }

  // 获取所有有效的输出指标
  getAvailableMetrics(): string[] {
    const metrics: string[] = [];
    if (this.getDps()) metrics.push("dps");
    if (this.getAverageDps()) metrics.push("averageDps");
    if (this.getTotalDamage()) metrics.push("totalDamage");
    if (this.getDph()) metrics.push("dph");
    if (this.getEps()) metrics.push("eps");
    if (this.getAverageEps()) metrics.push("averageEps");
    if (this.getTotalElementalDamage()) metrics.push("totalElementalDamage");
    if (this.getHps()) metrics.push("hps");
    if (this.getTotalHealing()) metrics.push("totalHealing");
    if (this.getAverageHps()) metrics.push("averageHps");
    return metrics;
  }
  
  // 处理技能描述，替换变量和清理富文本标记
  getProcessedDescription(): string {
    if (!this.skill || !this.skill.description) return '';
    
    // 先清理富文本标记
    let processedText = this.cleanRichTextTags(this.skill.description);
    
    // 替换变量
    processedText = this.replaceVariables(processedText);
    
    // 替换转义的换行符
    processedText = processedText.replace(/\\n/g, '\n');
    
    return processedText;
  }
  
  // 公共方法：处理任意描述文本
  processDescription(description: string, blackboard?: Array<{key: string, value: number}>): string {
    if (!description) return '';
    
    // 先清理富文本标记
    let processedText = this.cleanRichTextTags(description);
    
    // 替换变量 - 如果提供了blackboard则使用它，否则使用技能的blackboard
    processedText = this.replaceVariables(processedText, blackboard);
    
    // 替换转义的换行符
    processedText = processedText.replace(/\\n/g, '\n');
    
    return processedText;
  }
  
  // 替换描述中的变量
  protected replaceVariables(text: string, customBlackboard?: Array<{key: string, value: number}>): string {
    if (!text) return '';
    
    // 正则表达式匹配{key:default}格式
    const variableRegex = /{([^:}]+)(:([^}]*))?}/g;
    
    return text.replace(variableRegex, (match, key, _, defaultValue) => {
      // 处理duration等常规参数
      if (key === 'duration' && this.skill.duration) {
        return this.skill.duration.toString();
      }
      
      // 确定使用哪个blackboard
      const blackboard = customBlackboard || (this.skill && this.skill.blackboard);
      
      // 处理blackboard中的参数
      if (blackboard && Array.isArray(blackboard)) {
        const blackboardItem = blackboard.find((item: any) => item.key === key);
        
        if (blackboardItem) {
          // 根据值的类型返回正确的格式
          let value = blackboardItem.value;
          
          // 如果是百分比值（以%结尾或在match中有%指示）
          if (match.includes('%')) {
            // 将小数转为百分比
            return `${Math.round(value * 100)}%`;
          }
          
          // 使用valueStr或value
          return blackboardItem.valueStr || value.toString();
        }
      }
      
      // 没找到就使用默认值，如果有的话
      return defaultValue || match;
    });
  }
  
  // 清理富文本标记，移除所有格式标签
  protected cleanRichTextTags(text: string): string {
    if (!text) return '';
    
    // 移除所有<@ba.xxx>内容</>格式的标签
    let cleanedText = text.replace(/<@ba\.[^>]*>([^<]*)<\/>/g, '$1');
    
    // 移除所有<@xxx>内容</>格式的标签
    cleanedText = cleanedText.replace(/<@[^>]*>([^<]*)<\/>/g, '$1');
    
    // 移除所有<$ba.xxx>格式的标签
    cleanedText = cleanedText.replace(/<\$ba\.[^>]*>/g, '');
    
    // 移除所有其他<$xxx>格式的标签
    cleanedText = cleanedText.replace(/<\$[^>]*>/g, '');
    
    // 移除所有单独的</>标签
    cleanedText = cleanedText.replace(/<\/>/g, '');
    
    return cleanedText;
  }

  // 基础计算方法
  protected calculateBaseDamage(): number {
    // 基础伤害计算逻辑
    return 0;
  }
}

// 技能计算器工厂接口
export interface SkillCalculatorFactory {
  createCalculator(
    character: any,
    skill: any,
    skillLevel: number,
    options: SkillOptions
  ): SkillCalculator;
}