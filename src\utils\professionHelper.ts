// 职业代号到中文名称的映射
const PROFESSION_MAP: Record<string, string> = {
  "vanguard": "先锋",
  "guard": "近卫",
  "sniper": "狙击",
  "tank": "重装",
  "medic": "医疗",
  "support": "辅助",
  "caster": "术师",
  "special": "特种",
  "VANGUARD": "先锋",
  "GUARD": "近卫",
  "SNIPER": "狙击",
  "TANK": "重装",
  "MEDIC": "医疗",
  "SUPPORT": "辅助",
  "CASTER": "术师",
  "SPECIAL": "特种"
};

/**
 * 将职业代号转换为中文名称
 * @param professionId 职业代号
 * @returns 职业中文名称，如果找不到对应的名称则返回原代号
 */
export function getProfessionName(professionId: string): string {
  // 先尝试直接匹配
  const directMatch = PROFESSION_MAP[professionId];
  if (directMatch) {
    return directMatch;
  }
  
  // 如果直接匹配失败，尝试转换为小写后匹配
  const lowerCaseMatch = PROFESSION_MAP[professionId.toLowerCase()];
  if (lowerCaseMatch) {
    return lowerCaseMatch;
  }
  
  // 如果都匹配失败，返回原代号
  return professionId;
}