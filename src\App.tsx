import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import Home from './pages/Home';
import DpsCalculator from './pages/DpsCalculator';
import EnemyDatabase from './pages/EnemyDatabase';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<MainLayout />}>
          <Route index element={<Home />} />
          <Route path="dps-calculator" element={<DpsCalculator />} />
          <Route path="enemy-database" element={<EnemyDatabase />} />
        </Route>
      </Routes>
    </Router>
  );
}

export default App; 