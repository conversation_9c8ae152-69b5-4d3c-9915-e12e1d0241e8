import { CharacterData, CharacterSkill } from '../types/character';
import { SkillOptions } from '../types/skill';

// 伤害类型枚举
export enum DamageType {
  PHYSICAL = 'physical',
  MAGICAL = 'magical',
  ELEMENTAL = 'elemental',
  TRUE = 'true',
  NORMAL = 'normal',
  NONE = 'none'
}

export abstract class SkillCalculator {
    // 构造函数接收必要参数
    constructor(
      protected character: CharacterData,
      protected skill: CharacterSkill,
      protected skillLevel: number,
      protected options: SkillOptions
    ) {}

    // 基础输出方法 - 子类需覆盖实现
    getDps(): string { return ""; }
    getAverageDps(): string { return ""; }
    getTotalDamage(): string { return ""; }
    getDph(): string { return ""; }
    getEps(): string { return ""; }
    getAverageEps(): string { return ""; }
    getTotalElementalDamage(): string { return ""; }
    getHps(): string { return ""; }
    getTotalHealing(): string { return ""; }
    getAverageHps(): string { return ""; }

    // 获取伤害类型 - 子类应覆盖此方法指定伤害类型
    protected getDamageType(): DamageType {
      return DamageType.NONE;
    }

    // 辅助方法：获取所有有效的输出项
    getAvailableMetrics(): string[] {
      const metrics: string[] = [];
      if (this.getDps()) metrics.push("dps");
      if (this.getAverageDps()) metrics.push("averageDps");
      if (this.getTotalDamage()) metrics.push("totalDamage");
      if (this.getDph()) metrics.push("dph");
      if (this.getEps()) metrics.push("eps");
      if (this.getAverageEps()) metrics.push("averageEps");
      if (this.getTotalElementalDamage()) metrics.push("totalElementalDamage");
      if (this.getHps()) metrics.push("hps");
      if (this.getTotalHealing()) metrics.push("totalHealing");
      if (this.getAverageHps()) metrics.push("averageHps");
      return metrics;
    }

    // ======== 以下是通用的伤害计算方法 ========

    /**
     * 计算技能伤害
     * 统一处理各种乘区，自动根据伤害类型应用正确的公式
     */
    protected calculateDamage(baseAtk: number): number {
      // 各乘区数值获取（包含手动输入的加成）
      const directAtkAddition = this.getDirectAtkAddition() + (this.options.directAtkAddition || 0);
      const directAtkMultiplier = this.getDirectAtkMultiplier() + (this.options.directAtkMultiplier || 0);
      const finalAtkAddition = this.getFinalAtkAddition() + (this.options.finalAtkAddition || 0);
      const skillMultiplier = this.getSkillMultiplier() * (1 + (this.options.skillMultiplierBonus || 0));
      const damageAddition = this.getDamageAddition() + (this.options.damageAddition || 0);
      const finalDamageMultiplier = this.getFinalDamageMultiplier() * (1 + (this.options.finalDamageMultiplier || 0));

      // 计算攻击力
      const attackPower = (baseAtk + directAtkAddition) * (1 + directAtkMultiplier) + finalAtkAddition;

      // 计算技能伤害(未计算敌人抗性)
      const rawDamage = attackPower * skillMultiplier + damageAddition;

      // 根据伤害类型计算最终伤害
      let finalDamage = 0;

      switch (this.getDamageType()) {
        case DamageType.PHYSICAL:
          // 获取敌人防御力和防御力减免（包含手动输入的减免）
          const enemyDef = this.options.enemyDef || 0;
          const defReduction = this.getEnemyDefenseReduction() + (this.options.enemyDefReduction || 0);

          // 计算物理伤害
          const actualDef = Math.max(0, enemyDef - defReduction);
          const damageWithDef = rawDamage - actualDef;
          const minimumDamage = rawDamage * 0.05; // 保底5%伤害

          finalDamage = Math.max(damageWithDef, minimumDamage);
          break;

        case DamageType.MAGICAL:
          // 获取敌人法抗和法抗减免（包含手动输入的减免）
          const enemyRes = this.options.enemyRes || 0;
          const resReduction = this.getEnemyResistanceReduction() + (this.options.enemyResReduction || 0) * 100;

          // 计算法术伤害
          const actualRes = Math.max(Math.min(enemyRes - resReduction, 95), 5); // 法抗有5%~95%限制
          finalDamage = rawDamage * (1 - actualRes / 100);
          break;

        case DamageType.ELEMENTAL:
          // 获取敌人元素抗性
          const enemyEleRes = this.options.enemyElementalRes || 0;

          // 计算元素伤害
          const actualEleRes = Math.max(Math.min(enemyEleRes, 100), 0); // 元素抗性有0%~100%限制
          finalDamage = rawDamage * (1 - actualEleRes / 100);
          break;

        case DamageType.TRUE:
          // 真实伤害无视抗性
          finalDamage = rawDamage;
          break;

        default:
          finalDamage = 0;
      }

      // 应用最终伤害乘区
      return finalDamage * finalDamageMultiplier;
    }

    /**
     * 帧对齐计算
     * 游戏中每秒30帧，攻击间隔会被舍入到最接近的帧
     */
    protected calculateFrameAlignment(interval: number, attackSpeedBonus: number = 0): number {
      // 计算实际间隔并进行帧对齐(每秒30帧)
      const actualInterval = interval / ((100 + attackSpeedBonus) / 100);
      const frameCount = Math.round(actualInterval * 30);
      const alignedInterval = frameCount / 30;

      return alignedInterval;
    }

    /**
     * 以下是各个乘区的默认实现
     * 子类可以覆盖这些方法提供技能特定的值
     */

    // A区: 攻击力直接加算
    protected getDirectAtkAddition(): number {
      return 0;
    }

    // B区: 攻击力直接乘算
    protected getDirectAtkMultiplier(): number {
      return 0;
    }

    // C区: 攻击力最终加算
    protected getFinalAtkAddition(): number {
      return 0;
    }

    // D区: 攻击力最终乘算(技能倍率)
    protected getSkillMultiplier(): number {
      return 1;
    }

    // E区: 伤害加算
    protected getDamageAddition(): number {
      return 0;
    }

    // F区: 伤害最终乘算
    protected getFinalDamageMultiplier(): number {
      return 1;
    }

    // 敌人防御力减免
    protected getEnemyDefenseReduction(): number {
      return 0;
    }

    // 敌人法术抗性减免
    protected getEnemyResistanceReduction(): number {
      return 0;
    }
  }